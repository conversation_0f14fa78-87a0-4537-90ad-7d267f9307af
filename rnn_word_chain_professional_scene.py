#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RNN算法 - 词语接龙高手场景
基于ProfessionalScienceTemplate实现的完整Manim动画
"""

from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate

class RNNWordChainProfessionalScene(ProfessionalScienceTemplate):
    """RNN算法词语接龙高手专业教学场景"""
    
    def construct(self):
        # 必须调用父类背景设置
        self.setup_background()
        
        # 执行各个阶段
        self.stage_1_introduction()
        self.stage_2_input_sequence()
        self.stage_3_first_word()
        self.stage_4_second_word()
        self.stage_5_third_word()
        self.stage_6_prediction()
        self.stage_7_application()

    def create_word_vector_group(self, words, vectors):
        """创建词向量组"""
        word_vector_group = VGroup()
        
        for i, (word, vector) in enumerate(zip(words, vectors)):
            # 创建词语文字
            word_text = Text(word, font_size=28, color=WHITE)
            
            # 创建向量矩形框
            vector_rect = Rectangle(width=1.2, height=0.6, color=self.colors['primary'], stroke_width=2)
            vector_text = Text(str(vector), font_size=14, color=WHITE)
            vector_group = VGroup(vector_rect, vector_text)
            
            # 组合词语和向量
            word_vector = VGroup(word_text, vector_group).arrange(DOWN, buff=0.2)
            word_vector_group.add(word_vector)
        
        word_vector_group.arrange(RIGHT, buff=0.8)
        return word_vector_group
    
    def create_hidden_state_group(self, state_values, label="隐藏状态"):
        """创建隐藏状态组"""
        label_text = Text(label, font_size=16, color=WHITE)
        state_rect = Rectangle(width=1.5, height=0.6, color=self.colors['secondary'], stroke_width=2)
        state_text = Text(str(state_values), font_size=14, color=WHITE)
        
        state_group = VGroup(state_rect, state_text)
        hidden_state = VGroup(label_text, state_group).arrange(DOWN, buff=0.1)
        return hidden_state
    
    def create_rnn_formula_group(self):
        """创建RNN计算公式组"""
        formula = MathTex(r"h_t = f(W_{xh}x_t + W_{hh}h_{t-1})", font_size=24, color=WHITE)
        formula_rect = SurroundingRectangle(formula, color=self.colors['accent'], stroke_width=2)
        return VGroup(formula_rect, formula)
    
    def create_output_formula_group(self):
        """创建输出计算公式组"""
        formula = MathTex(r"y_t = W_{hy}h_t", font_size=24, color=WHITE)
        formula_rect = SurroundingRectangle(formula, color=self.colors['accent'], stroke_width=2)
        return VGroup(formula_rect, formula)
    
    def create_probability_table(self):
        """创建概率分布表格"""
        # 表格头
        header = VGroup(
            Text("词语", font_size=16, color=WHITE),
            Text("概率", font_size=16, color=WHITE)
        ).arrange(RIGHT, buff=0.8)
        
        # 数据行
        row1 = VGroup(
            Text("天安门", font_size=16, color=self.colors['highlight']),
            Text("0.85", font_size=16, color=self.colors['highlight'])
        ).arrange(RIGHT, buff=0.8)
        
        row2 = VGroup(
            Text("广场", font_size=16, color=WHITE),
            Text("0.10", font_size=16, color=WHITE)
        ).arrange(RIGHT, buff=0.8)
        
        row3 = VGroup(
            Text("其他", font_size=16, color=WHITE),
            Text("0.05", font_size=16, color=WHITE)
        ).arrange(RIGHT, buff=0.8)
        
        table = VGroup(header, row1, row2, row3).arrange(DOWN, buff=0.2)
        table_rect = SurroundingRectangle(table, color=WHITE, stroke_width=1)
        
        return VGroup(table_rect, table)
    
    def create_ai_figure(self):
        """创建AI形象"""
        head = Circle(radius=0.6, color=self.colors['primary'], fill_opacity=0.3)
        eye_left = Circle(radius=0.08, color=WHITE, fill_opacity=1).shift(LEFT*0.25 + UP*0.15)
        eye_right = Circle(radius=0.08, color=WHITE, fill_opacity=1).shift(RIGHT*0.25 + UP*0.15)
        mouth = Arc(radius=0.25, start_angle=-PI/3, angle=PI/3, color=WHITE, stroke_width=2).shift(DOWN*0.15)
        
        ai_figure = VGroup(head, eye_left, eye_right, mouth)
        return ai_figure
    
    def create_speech_bubble(self, text):
        """创建对话气泡"""
        bubble_text = Text(text, font_size=14, color=WHITE)
        bubble_rect = RoundedRectangle(
            width=bubble_text.width + 0.6, 
            height=bubble_text.height + 0.3,
            corner_radius=0.15,
            color=self.colors['auxiliary_bg'],
            fill_opacity=0.9,
            stroke_color=WHITE,
            stroke_width=1
        )
        
        return VGroup(bubble_rect, bubble_text)

    def stage_1_introduction(self):
        """阶段1：引入词语接龙与AI选手"""
        # 创建标题（严格8字以内）
        title_group = self.create_title_region_content("词语接龙")
        
        # 创建步骤（严格12字以内）
        step_group = self.create_step_region_content("AI大脑记忆")
        
        # 创建主内容
        ai_figure = self.create_ai_figure()
        speech_bubble = self.create_speech_bubble("记忆，就是RNN")
        speech_bubble.next_to(ai_figure, UP+RIGHT, buff=0.3)
        
        main_content_group = VGroup(ai_figure, speech_bubble)
        main_group = self.create_main_region_content(main_content_group)
        
        # 创建左辅助区域（标题6字以内，项目5个以内，每项15字以内）
        left_aux = self.create_left_auxiliary_content("概念", [
            "• RNN循环神经网络",
            "• 具有记忆能力",
            "• 处理序列数据",
            "• 词语接龙应用"
        ])
        
        # 动画序列
        self.play(Write(title_group), Write(step_group), run_time=1.5)
        self.play(FadeIn(main_group), run_time=1.5)
        self.play(FadeIn(left_aux), run_time=1)
        self.wait(2)
        
        # 保存当前元素引用，用于后续更新
        self.current_title = title_group
        self.current_step = step_group
        self.current_main = main_group
        self.current_left_aux = left_aux

    def stage_2_input_sequence(self):
        """阶段2：输入词语序列"""
        # 更新步骤
        new_step = self.create_step_region_content("输入词语序列")
        
        # 创建新的主内容
        words = ["我", "爱", "北京"]
        vectors = ["[1,0,0]", "[0,1,0]", "[0,0,1]"]
        word_vector_group = self.create_word_vector_group(words, vectors)
        new_main_group = self.create_main_region_content(word_vector_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("数据", [
            "• 序列: ['我', '爱', '北京']",
            "• 词向量编码",
            "• 维度: 3",
            "• 顺序输入处理"
        ])
        
        # 创建右辅助区域
        right_aux = self.create_right_auxiliary_content("特点", [
            "• 每词一个向量",
            "• 独热编码",
            "• 序列保持",
            "• 逐步处理"
        ])
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(
            Transform(self.current_left_aux, new_left_aux),
            FadeIn(right_aux), 
            run_time=1
        )
        self.wait(1)
        
        # 更新引用
        self.current_step = new_step
        self.current_main = new_main_group
        self.current_left_aux = new_left_aux
        self.current_right_aux = right_aux

    def stage_3_first_word(self):
        """阶段3：处理第一个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第一个词")
        
        # 创建新的主内容
        current_word = self.create_word_vector_group(["我"], ["[1,0,0]"])[0]
        initial_hidden_state = self.create_hidden_state_group([0, 0], "初始状态")
        formula_group = self.create_rnn_formula_group()
        calculated_hidden_state = self.create_hidden_state_group([0.2, 0.4], "新状态")
        
        # 布局主内容组
        top_row = VGroup(current_word, initial_hidden_state).arrange(RIGHT, buff=1.0)
        bottom_row = VGroup(formula_group, calculated_hidden_state).arrange(RIGHT, buff=1.0)
        main_content_group = VGroup(top_row, bottom_row).arrange(DOWN, buff=0.8)
        
        new_main_group = self.create_main_region_content(main_content_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("数据", [
            "• 当前词: ['我']",
            "• 初始状态: [0,0]",
            "• 权重矩阵计算",
            "• 输出新状态"
        ])
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content("公式", [
            MathTex(r"W_{xh}x_t", font_size=14),
            MathTex(r"W_{hh}h_{t-1}", font_size=14),
            "• 激活函数",
            "• 状态更新"
        ])
        
        # 创建结果区域（严格40字以内）
        result_group = self.create_result_region_content("第一个词处理完成，生成新的隐藏状态[0.2, 0.4]")
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(
            Transform(self.current_left_aux, new_left_aux),
            Transform(self.current_right_aux, new_right_aux),
            run_time=1
        )
        self.play(Write(result_group), run_time=1)
        self.wait(1.5)
        
        # 更新引用
        self.current_step = new_step
        self.current_main = new_main_group
        self.current_left_aux = new_left_aux
        self.current_right_aux = new_right_aux
        self.current_result = result_group

    def stage_4_second_word(self):
        """阶段4：处理第二个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第二个词")
        
        # 创建新的主内容
        current_word = self.create_word_vector_group(["爱"], ["[0,1,0]"])[0]
        prev_hidden_state = self.create_hidden_state_group([0.2, 0.4], "上一状态")
        formula_group = self.create_rnn_formula_group()
        calculated_hidden_state = self.create_hidden_state_group([0.6, 0.8], "新状态")
        
        # 布局主内容组
        top_row = VGroup(current_word, prev_hidden_state).arrange(RIGHT, buff=1.0)
        bottom_row = VGroup(formula_group, calculated_hidden_state).arrange(RIGHT, buff=1.0)
        main_content_group = VGroup(top_row, bottom_row).arrange(DOWN, buff=0.8)
        
        new_main_group = self.create_main_region_content(main_content_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("数据", [
            "• 当前词: ['爱']",
            "• 上一状态: [0.2,0.4]",
            "• 状态传递计算",
            "• 记忆积累"
        ])
        
        # 更新结果区域
        new_result = self.create_result_region_content('状态更新为[0.6, 0.8]，包含"我爱"信息')
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(Transform(self.current_left_aux, new_left_aux), run_time=1)
        self.play(Transform(self.current_result, new_result), run_time=1)
        self.wait(1.5)
        
        # 更新引用
        self.current_step = new_step
        self.current_main = new_main_group
        self.current_left_aux = new_left_aux
        self.current_result = new_result

    def stage_5_third_word(self):
        """阶段5：处理第三个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第三个词")
        
        # 创建新的主内容
        current_word = self.create_word_vector_group(["北京"], ["[0,0,1]"])[0]
        prev_hidden_state = self.create_hidden_state_group([0.6, 0.8], "上一状态")
        formula_group = self.create_rnn_formula_group()
        final_hidden_state = self.create_hidden_state_group([0.9, 0.7], "最终状态")
        
        # 布局主内容组
        top_row = VGroup(current_word, prev_hidden_state).arrange(RIGHT, buff=1.0)
        bottom_row = VGroup(formula_group, final_hidden_state).arrange(RIGHT, buff=1.0)
        main_content_group = VGroup(top_row, bottom_row).arrange(DOWN, buff=0.8)
        
        new_main_group = self.create_main_region_content(main_content_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("数据", [
            "• 当前词: ['北京']",
            "• 上一状态: [0.6,0.8]",
            "• 完整序列处理",
            "• 语义理解完成"
        ])
        
        # 更新结果区域
        new_result = self.create_result_region_content('最终状态[0.9, 0.7]，理解完整"我爱北京"')
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(Transform(self.current_left_aux, new_left_aux), run_time=1)
        self.play(Transform(self.current_result, new_result), run_time=1)
        self.wait(1.5)
        
        # 更新引用
        self.current_step = new_step
        self.current_main = new_main_group
        self.current_left_aux = new_left_aux
        self.current_result = new_result

    def stage_6_prediction(self):
        """阶段6：预测下一个词"""
        # 更新步骤
        new_step = self.create_step_region_content("预测下一个词")
        
        # 创建新的主内容
        final_hidden_state = self.create_hidden_state_group([0.9, 0.7], "最终状态")
        output_formula = self.create_output_formula_group()
        probability_table = self.create_probability_table()
        
        # 布局主内容组
        main_content_group = VGroup(
            final_hidden_state, 
            output_formula, 
            probability_table
        ).arrange(DOWN, buff=0.6)
        
        new_main_group = self.create_main_region_content(main_content_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("数据", [
            "• 最终状态: [0.9,0.7]",
            "• 输出层计算",
            "• 概率分布",
            "• 选择最大概率"
        ])
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content("结果", [
            "• 天安门: 85%",
            "• 广场: 10%", 
            "• 其他: 5%",
            "• 预测成功"
        ])
        
        # 更新结果区域
        new_result = self.create_result_region_content('预测下一个词："天安门"（概率最高85%）')
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(
            Transform(self.current_left_aux, new_left_aux),
            Transform(self.current_right_aux, new_right_aux),
            run_time=1
        )
        self.play(Transform(self.current_result, new_result), run_time=1)
        self.wait(2)
        
        # 更新引用
        self.current_step = new_step
        self.current_main = new_main_group
        self.current_left_aux = new_left_aux
        self.current_right_aux = new_right_aux
        self.current_result = new_result

    def stage_7_application(self):
        """阶段7：实际应用 - 继续接龙"""
        # 更新步骤
        new_step = self.create_step_region_content("实际应用接龙")
        
        # 创建新的主内容
        # 显示完整序列
        words = ["我", "爱", "北京", "天安门"]
        word_texts = VGroup()
        for i, word in enumerate(words):
            if i < 3:
                word_text = Text(word, font_size=24, color=WHITE)
            else:
                word_text = Text(word, font_size=24, color=self.colors['highlight'])
            word_texts.add(word_text)
        
        word_texts.arrange(RIGHT, buff=0.6)
        
        # 添加循环指示
        ai_figure_small = self.create_ai_figure().scale(0.5)
        loop_text = Text("🔄", font_size=20, color=self.colors['accent'])
        ai_loop_group = VGroup(ai_figure_small, loop_text).arrange(DOWN, buff=0.1)
        
        main_content_group = VGroup(word_texts, ai_loop_group).arrange(DOWN, buff=1.0)
        new_main_group = self.create_main_region_content(main_content_group)
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content("应用", [
            "• 输入: ['我', '爱', '北京']",
            "• 预测: ['天安门']",
            "• 可继续接龙",
            "• 无限循环能力"
        ])
        
        # 更新右辅助区域 - 清空
        new_right_aux = self.create_right_auxiliary_content("优势", [
            "• 记忆前文信息",
            "• 语义理解能力",
            "• 上下文关联",
            "• 智能预测"
        ])
        
        # 最终结果区域
        final_result = self.create_result_region_content("RNN成功实现词语接龙，展现强大的序列理解和生成能力")
        
        # 动画序列
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.play(ReplacementTransform(self.current_main, new_main_group), run_time=1.5)
        self.play(
            Transform(self.current_left_aux, new_left_aux),
            Transform(self.current_right_aux, new_right_aux),
            run_time=1
        )
        self.play(Transform(self.current_result, final_result), run_time=1)
        self.wait(3)
        
        # 结束动画 - 渐出所有元素
        self.play(
            FadeOut(self.current_title),
            FadeOut(self.current_step),
            FadeOut(self.current_main),
            FadeOut(self.current_left_aux),
            FadeOut(self.current_right_aux),
            FadeOut(final_result),
            run_time=2
        )
        self.wait(1)

# 配置场景
if __name__ == "__main__":
    # 场景配置
    config.pixel_height = 1080
    config.pixel_width = 1920
    config.frame_rate = 30 