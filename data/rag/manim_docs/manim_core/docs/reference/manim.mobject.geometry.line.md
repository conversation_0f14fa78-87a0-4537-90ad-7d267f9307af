# line

Mobjects that are lines or variations of them.

### Type Aliases

### *class* AngleQuadrant

```default
tuple[Literal[-1, 1], Literal[-1, 1]]
```

### Classes

| [`Angle`](manim.mobject.geometry.line.Angle.md#manim.mobject.geometry.line.Angle)                   | A circular arc or elbow-type mobject representing an angle of two lines.                                                                                             |
|-----------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`Arrow`](manim.mobject.geometry.line.Arrow.md#manim.mobject.geometry.line.Arrow)                   | An arrow.                                                                                                                                                            |
| [`DashedLine`](manim.mobject.geometry.line.DashedLine.md#manim.mobject.geometry.line.DashedLine)    | A dashed [`Line`](manim.mobject.geometry.line.Line.md#manim.mobject.geometry.line.Line).                                                                             |
| [`DoubleArrow`](manim.mobject.geometry.line.DoubleArrow.md#manim.mobject.geometry.line.DoubleArrow) | An arrow with tips on both ends.                                                                                                                                     |
| [`Elbow`](manim.mobject.geometry.line.Elbow.md#manim.mobject.geometry.line.Elbow)                   | Two lines that create a right angle about each other: L-shape.                                                                                                       |
| [`Line`](manim.mobject.geometry.line.Line.md#manim.mobject.geometry.line.Line)                      |                                                                                                                                                                      |
| [`RightAngle`](manim.mobject.geometry.line.RightAngle.md#manim.mobject.geometry.line.RightAngle)    | An elbow-type mobject representing a right angle between two lines.                                                                                                  |
| [`TangentLine`](manim.mobject.geometry.line.TangentLine.md#manim.mobject.geometry.line.TangentLine) | Constructs a line tangent to a [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject) at a specific point. |
| [`Vector`](manim.mobject.geometry.line.Vector.md#manim.mobject.geometry.line.Vector)                | A vector specialized for use in graphs.                                                                                                                              |
