# manim_colors

Colors included in the global name space.

These colors form <PERSON><PERSON>’s default color space.

![image](media/images/ColorsOverview-1.png)

| Color Name     | RGB Hex Code                                                                                                                                     | Color Name     | RGB Hex Code                                                                                                                                     |
|----------------|--------------------------------------------------------------------------------------------------------------------------------------------------|----------------|--------------------------------------------------------------------------------------------------------------------------------------------------|
| `BLACK`        | <div style="background-color:#000000;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#000000</code></div> | `BLUE`         | <div style="background-color:#58C4DD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#58C4DD</code></div> |
| `BLUE_A`       | <div style="background-color:#C7E9F1;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#C7E9F1</code></div> | `BLUE_B`       | <div style="background-color:#9CDCEB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#9CDCEB</code></div> |
| `BLUE_C`       | <div style="background-color:#58C4DD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#58C4DD</code></div> | `BLUE_D`       | <div style="background-color:#29ABCA;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#29ABCA</code></div> |
| `BLUE_E`       | <div style="background-color:#236B8E;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#236B8E</code></div> | `DARKER_GRAY`  | <div style="background-color:#222222;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#222222</code></div> |
| `DARKER_GREY`  | <div style="background-color:#222222;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#222222</code></div> | `DARK_BLUE`    | <div style="background-color:#236B8E;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#236B8E</code></div> |
| `DARK_BROWN`   | <div style="background-color:#8B4513;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#8B4513</code></div> | `DARK_GRAY`    | <div style="background-color:#444444;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#444444</code></div> |
| `DARK_GREY`    | <div style="background-color:#444444;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#444444</code></div> | `GOLD`         | <div style="background-color:#F0AC5F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F0AC5F</code></div> |
| `GOLD_A`       | <div style="background-color:#F7C797;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F7C797</code></div> | `GOLD_B`       | <div style="background-color:#F9B775;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F9B775</code></div> |
| `GOLD_C`       | <div style="background-color:#F0AC5F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F0AC5F</code></div> | `GOLD_D`       | <div style="background-color:#E1A158;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#E1A158</code></div> |
| `GOLD_E`       | <div style="background-color:#C78D46;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#C78D46</code></div> | `GRAY`         | <div style="background-color:#888888;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#888888</code></div> |
| `GRAY_A`       | <div style="background-color:#DDDDDD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DDDDDD</code></div> | `GRAY_B`       | <div style="background-color:#BBBBBB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#BBBBBB</code></div> |
| `GRAY_BROWN`   | <div style="background-color:#736357;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#736357</code></div> | `GRAY_C`       | <div style="background-color:#888888;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#888888</code></div> |
| `GRAY_D`       | <div style="background-color:#444444;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#444444</code></div> | `GRAY_E`       | <div style="background-color:#222222;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#222222</code></div> |
| `GREEN`        | <div style="background-color:#83C167;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#83C167</code></div> | `GREEN_A`      | <div style="background-color:#C9E2AE;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#C9E2AE</code></div> |
| `GREEN_B`      | <div style="background-color:#A6CF8C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#A6CF8C</code></div> | `GREEN_C`      | <div style="background-color:#83C167;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#83C167</code></div> |
| `GREEN_D`      | <div style="background-color:#77B05D;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#77B05D</code></div> | `GREEN_E`      | <div style="background-color:#699C52;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#699C52</code></div> |
| `GREY`         | <div style="background-color:#888888;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#888888</code></div> | `GREY_A`       | <div style="background-color:#DDDDDD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DDDDDD</code></div> |
| `GREY_B`       | <div style="background-color:#BBBBBB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#BBBBBB</code></div> | `GREY_BROWN`   | <div style="background-color:#736357;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#736357</code></div> |
| `GREY_C`       | <div style="background-color:#888888;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#888888</code></div> | `GREY_D`       | <div style="background-color:#444444;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#444444</code></div> |
| `GREY_E`       | <div style="background-color:#222222;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#222222</code></div> | `LIGHTER_GRAY` | <div style="background-color:#DDDDDD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DDDDDD</code></div> |
| `LIGHTER_GREY` | <div style="background-color:#DDDDDD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DDDDDD</code></div> | `LIGHT_BROWN`  | <div style="background-color:#CD853F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#CD853F</code></div> |
| `LIGHT_GRAY`   | <div style="background-color:#BBBBBB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#BBBBBB</code></div> | `LIGHT_GREY`   | <div style="background-color:#BBBBBB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#BBBBBB</code></div> |
| `LIGHT_PINK`   | <div style="background-color:#DC75CD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DC75CD</code></div> | `LOGO_BLACK`   | <div style="background-color:#343434;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#343434</code></div> |
| `LOGO_BLUE`    | <div style="background-color:#525893;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#525893</code></div> | `LOGO_GREEN`   | <div style="background-color:#87C2A5;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#87C2A5</code></div> |
| `LOGO_RED`     | <div style="background-color:#E07A5F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#E07A5F</code></div> | `LOGO_WHITE`   | <div style="background-color:#ECE7E2;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#ECE7E2</code></div> |
| `MAROON`       | <div style="background-color:#C55F73;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#C55F73</code></div> | `MAROON_A`     | <div style="background-color:#ECABC1;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#ECABC1</code></div> |
| `MAROON_B`     | <div style="background-color:#EC92AB;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#EC92AB</code></div> | `MAROON_C`     | <div style="background-color:#C55F73;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#C55F73</code></div> |
| `MAROON_D`     | <div style="background-color:#A24D61;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#A24D61</code></div> | `MAROON_E`     | <div style="background-color:#94424F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#94424F</code></div> |
| `ORANGE`       | <div style="background-color:#FF862F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FF862F</code></div> | `PINK`         | <div style="background-color:#D147BD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#D147BD</code></div> |
| `PURE_BLUE`    | <div style="background-color:#0000FF;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#0000FF</code></div> | `PURE_GREEN`   | <div style="background-color:#00FF00;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00FF00</code></div> |
| `PURE_RED`     | <div style="background-color:#FF0000;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#FF0000</code></div> | `PURPLE`       | <div style="background-color:#9A72AC;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#9A72AC</code></div> |
| `PURPLE_A`     | <div style="background-color:#CAA3E8;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#CAA3E8</code></div> | `PURPLE_B`     | <div style="background-color:#B189C6;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#B189C6</code></div> |
| `PURPLE_C`     | <div style="background-color:#9A72AC;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#9A72AC</code></div> | `PURPLE_D`     | <div style="background-color:#715582;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#715582</code></div> |
| `PURPLE_E`     | <div style="background-color:#644172;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#644172</code></div> | `RED`          | <div style="background-color:#FC6255;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FC6255</code></div> |
| `RED_A`        | <div style="background-color:#F7A1A3;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F7A1A3</code></div> | `RED_B`        | <div style="background-color:#FF8080;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FF8080</code></div> |
| `RED_C`        | <div style="background-color:#FC6255;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FC6255</code></div> | `RED_D`        | <div style="background-color:#E65A4C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#E65A4C</code></div> |
| `RED_E`        | <div style="background-color:#CF5044;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#CF5044</code></div> | `TEAL`         | <div style="background-color:#5CD0B3;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#5CD0B3</code></div> |
| `TEAL_A`       | <div style="background-color:#ACEAD7;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#ACEAD7</code></div> | `TEAL_B`       | <div style="background-color:#76DDC0;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#76DDC0</code></div> |
| `TEAL_C`       | <div style="background-color:#5CD0B3;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#5CD0B3</code></div> | `TEAL_D`       | <div style="background-color:#55C1A7;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#55C1A7</code></div> |
| `TEAL_E`       | <div style="background-color:#49A88F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#49A88F</code></div> | `WHITE`        | <div style="background-color:#FFFFFF;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFFFFF</code></div> |
| `YELLOW`       | <div style="background-color:#FFFF00;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFFF00</code></div> | `YELLOW_A`     | <div style="background-color:#FFF1B6;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFF1B6</code></div> |
| `YELLOW_B`     | <div style="background-color:#FFEA94;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFEA94</code></div> | `YELLOW_C`     | <div style="background-color:#FFFF00;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFFF00</code></div> |
| `YELLOW_D`     | <div style="background-color:#F4D345;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F4D345</code></div> | `YELLOW_E`     | <div style="background-color:#E8C11C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#E8C11C</code></div> |
