# tex_mobject

Mobjects representing text rendered using LaTeX.

#### IMPORTANT
See the corresponding tutorial [Text With LaTeX](../guides/using_text.md#rendering-with-latex)

#### NOTE
Just as you can use [`Text`](manim.mobject.text.text_mobject.Text.md#manim.mobject.text.text_mobject.Text) (from the module [`text_mobject`](manim.mobject.text.text_mobject.md#module-manim.mobject.text.text_mobject)) to add text to your videos, you can use [`Tex`](manim.mobject.text.tex_mobject.Tex.md#manim.mobject.text.tex_mobject.Tex) and [`MathTex`](manim.mobject.text.tex_mobject.MathTex.md#manim.mobject.text.tex_mobject.MathTex) to insert LaTeX.

### Classes

| [`BulletedList`](manim.mobject.text.tex_mobject.BulletedList.md#manim.mobject.text.tex_mobject.BulletedList)                      | A bulleted list.                                         |
|-----------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------|
| [`MathTex`](manim.mobject.text.tex_mobject.MathTex.md#manim.mobject.text.tex_mobject.MathTex)                                     | A string compiled with LaTeX in math mode.               |
| [`SingleStringMathTex`](manim.mobject.text.tex_mobject.SingleStringMathTex.md#manim.mobject.text.tex_mobject.SingleStringMathTex) | Elementary building block for rendering text with LaTeX. |
| [`Tex`](manim.mobject.text.tex_mobject.Tex.md#manim.mobject.text.tex_mobject.Tex)                                                 | A string compiled with LaTeX in normal mode.             |
| [`Title`](manim.mobject.text.tex_mobject.Title.md#manim.mobject.text.tex_mobject.Title)                                           | A mobject representing an underlined title.              |
