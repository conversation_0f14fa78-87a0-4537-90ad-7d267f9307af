# ManimColorModuleDocumenter

Qualified name: `manim.utils.docbuild.autocolor\_directive.ManimColorModuleDocumenter`

### *class* ManimColorModuleDocumenter(name, arguments, options, content, lineno, content_offset, block_text, state, state_machine)

Bases: `Directive`

### Methods

| `add_directive_header`   |    |
|--------------------------|----|
| `run`                    |    |

### Attributes

| `final_argument_whitespace`                                                                                     | May the final argument contain whitespace?                 |
|-----------------------------------------------------------------------------------------------------------------|------------------------------------------------------------|
| [`has_content`](#manim.utils.docbuild.autocolor_directive.ManimColorModuleDocumenter.has_content)               | May the directive have content?                            |
| `objtype`                                                                                                       |                                                            |
| `option_spec`                                                                                                   | Mapping of option names to validator functions.            |
| `optional_arguments`                                                                                            | Number of optional arguments after the required arguments. |
| [`required_arguments`](#manim.utils.docbuild.autocolor_directive.ManimColorModuleDocumenter.required_arguments) | Number of required directive arguments.                    |

#### has_content *= True*

May the directive have content?

#### required_arguments *= 1*

Number of required directive arguments.
