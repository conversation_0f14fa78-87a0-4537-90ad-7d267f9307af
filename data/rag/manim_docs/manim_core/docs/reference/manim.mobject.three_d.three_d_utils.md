# three_d_utils

Utility functions for three-dimensional mobjects.

### Functions

### get_3d_vmob_end_corner(vmob)

* **Return type:**
  [*Point3D*](manim.typing.md#manim.typing.Point3D)

### get_3d_vmob_end_corner_index(vmob)

* **Return type:**
  int

### get_3d_vmob_end_corner_unit_normal(vmob)

* **Return type:**
  [*Vector3D*](manim.typing.md#manim.typing.Vector3D)

### get_3d_vmob_gradient_start_and_end_points(vmob)

* **Return type:**
  tuple[[*Point3D*](manim.typing.md#manim.typing.Point3D), [*Point3D*](manim.typing.md#manim.typing.Point3D)]

### get_3d_vmob_start_corner(vmob)

* **Return type:**
  [*Point3D*](manim.typing.md#manim.typing.Point3D)

### get_3d_vmob_start_corner_index(vmob)

* **Return type:**
  *Literal*[0]

### get_3d_vmob_start_corner_unit_normal(vmob)

* **Return type:**
  [*Vector3D*](manim.typing.md#manim.typing.Vector3D)

### get_3d_vmob_unit_normal(vmob, point_index)

* **Parameters:**
  **point_index** (*int*)
* **Return type:**
  [*Vector3D*](manim.typing.md#manim.typing.Vector3D)
