# autocolor_directive

A directive for documenting colors in Manim.

### Classes

| [`ManimColorModuleDocumenter`](manim.utils.docbuild.autocolor_directive.ManimColorModuleDocumenter.md#manim.utils.docbuild.autocolor_directive.ManimColorModuleDocumenter)   |    |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|

### Functions

### setup(app)

* **Parameters:**
  **app** (*Sphinx*)
* **Return type:**
  None
