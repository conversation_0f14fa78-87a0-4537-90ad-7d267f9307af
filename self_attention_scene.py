from manim import *
from prompts.professional_science_template import ProfessionalScienceTemplate


class SelfAttentionScene(ProfessionalScienceTemplate):
    """理解Self-Attention算法的可视化场景"""
    
    def construct(self):
        """构建场景"""
        # 必须调用
        self.setup_background()
        
        # 创建各个阶段的内容
        self.create_stage1_introduction()
        self.create_stage2_qkv_generation()
        self.create_stage3_attention_scores()
        self.create_stage4_scaling_softmax()
        self.create_stage5_weighted_sum()
        self.create_stage6_application()
    
    def create_stage1_introduction(self):
        """阶段1：引言与输入准备"""
        # 动作1：显示标题和步骤
        if self.region_elements["title"] is None:
            title = self.create_title_region_content("理解Self-Attention")
            self.play(Write(title))
            self.region_elements['title'] = title
        
        if self.region_elements["step"] is None:
            step = self.create_step_region_content("背景设定")
            self.play(Write(step))
            self.region_elements['step'] = step
        
        # 动作2：显示辅助信息和结果
        left_aux = self.create_left_auxiliary_content("主题:", ["数学定理"])
        right_aux = self.create_right_auxiliary_content("任务:", ["句子理解"])
        result = self.create_result_region_content("AI理解词语关联")
        
        self.play(FadeIn(left_aux), FadeIn(right_aux), Write(result))
        self.region_elements['left_auxiliary'] = left_aux
        self.region_elements['right_auxiliary'] = right_aux
        self.region_elements['result'] = result
        
        # 动作3：显示句子
        sentence_group = self.create_sentence_group()
        main_content = self.create_main_region_content(sentence_group)
        self.play(FadeIn(main_content))
        self.region_elements['main'] = main_content
        
        self.wait(1)
        
        # 动作4：更新标题和步骤
        new_title = self.create_title_region_content("步骤1：准备输入")
        new_step = self.create_step_region_content("词向量生成")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step
        
        # 动作5：更新辅助信息，句子变换为词向量
        new_left_aux = self.create_left_auxiliary_content("数据:", ["句子和向量"])
        new_right_aux = self.create_right_auxiliary_content("语义:", ["信息捕捉"])
        word_vector_group = self.create_word_vector_group()
        new_main = self.create_main_region_content(word_vector_group)
        
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux),
            ReplacementTransform(self.region_elements['main'], new_main)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux
        self.region_elements['main'] = new_main
        
        # 动作6：更新结果
        new_result = self.create_result_region_content("获得4个3维词向量")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)
    
    def create_sentence_group(self):
        """创建句子组"""
        words = ["我", "爱", "吃", "苹果"]
        word_objects = []
        
        for word in words:
            word_text = Text(word, font_size=36, color=WHITE)
            word_objects.append(word_text)
        
        sentence_group = VGroup(*word_objects)
        sentence_group.arrange(RIGHT, buff=0.8)
        return sentence_group
    
    def create_word_vector_group(self):
        """创建词向量组"""
        words = ["我", "爱", "吃", "苹果"]
        vectors = [
            [0.2, 0.8, 0.1],
            [0.7, 0.3, 0.9],
            [0.4, 0.6, 0.2],
            [0.9, 0.1, 0.7]
        ]
        
        word_vector_pairs = []
        
        for i, (word, vector) in enumerate(zip(words, vectors)):
            # 词文本
            word_text = Text(word, font_size=24, color=WHITE)
            
            # 向量框
            vector_matrix = Matrix([[f"{v:.1f}"] for v in vector], 
                                 bracket_h_buff=0.1, 
                                 bracket_v_buff=0.1,
                                 element_to_mobject_config={"font_size": 20})
            vector_matrix.set_color(BLUE)
            
            # 组合词和向量
            pair = VGroup(word_text, vector_matrix)
            pair.arrange(DOWN, buff=0.3)
            word_vector_pairs.append(pair)
        
        word_vector_group = VGroup(*word_vector_pairs)
        word_vector_group.arrange(RIGHT, buff=0.6)
        return word_vector_group

    def create_stage2_qkv_generation(self):
        """阶段2：生成QKV向量"""
        # 动作1：更新标题和步骤
        new_title = self.create_title_region_content("步骤2：生成QKV向量")
        new_step = self.create_step_region_content("词向量映射")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作2：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据:", ["词向量", "权重矩阵"])
        new_right_aux = self.create_right_auxiliary_content("生成:", ["Q、K、V"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作3：显示QKV矩阵和词向量相乘过程
        qkv_group = self.create_qkv_matrix_group()
        word_vectors = self.create_word_vector_group()

        # 组合词向量和QKV矩阵
        combined_group = VGroup(word_vectors, qkv_group)
        combined_group.arrange(DOWN, buff=0.8)

        new_main = self.create_main_region_content(combined_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 添加箭头指示相乘过程
        arrows = self.create_multiplication_arrows(word_vectors, qkv_group)
        self.play(Create(arrows))

        # 动作4：更新结果
        new_result = self.create_result_region_content("获得4组QKV向量")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

        self.wait(2)

    def create_qkv_matrix_group(self):
        """创建QKV权重矩阵组"""
        # 创建三个权重矩阵 W^Q, W^K, W^V
        wq_data = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6], [0.7, 0.8, 0.9]]
        wk_data = [[0.9, 0.8, 0.7], [0.6, 0.5, 0.4], [0.3, 0.2, 0.1]]
        wv_data = [[0.5, 0.5, 0.5], [0.3, 0.7, 0.2], [0.8, 0.1, 0.9]]

        # 创建矩阵对象
        wq_matrix = Matrix([[f"{val:.1f}" for val in row] for row in wq_data],
                          bracket_h_buff=0.1, bracket_v_buff=0.1,
                          element_to_mobject_config={"font_size": 16})
        wk_matrix = Matrix([[f"{val:.1f}" for val in row] for row in wk_data],
                          bracket_h_buff=0.1, bracket_v_buff=0.1,
                          element_to_mobject_config={"font_size": 16})
        wv_matrix = Matrix([[f"{val:.1f}" for val in row] for row in wv_data],
                          bracket_h_buff=0.1, bracket_v_buff=0.1,
                          element_to_mobject_config={"font_size": 16})

        # 设置颜色
        wq_matrix.set_color(RED)
        wk_matrix.set_color(GREEN)
        wv_matrix.set_color(BLUE)

        # 添加标签
        wq_label = MathTex("W^Q", font_size=24, color=RED)
        wk_label = MathTex("W^K", font_size=24, color=GREEN)
        wv_label = MathTex("W^V", font_size=24, color=BLUE)

        # 组合矩阵和标签
        wq_group = VGroup(wq_label, wq_matrix)
        wq_group.arrange(DOWN, buff=0.2)

        wk_group = VGroup(wk_label, wk_matrix)
        wk_group.arrange(DOWN, buff=0.2)

        wv_group = VGroup(wv_label, wv_matrix)
        wv_group.arrange(DOWN, buff=0.2)

        # 水平排列三个矩阵组
        qkv_group = VGroup(wq_group, wk_group, wv_group)
        qkv_group.arrange(RIGHT, buff=0.8)

        return qkv_group

    def create_multiplication_arrows(self, word_vectors, qkv_matrices):
        """创建表示矩阵相乘的箭头"""
        arrows = VGroup()

        # 从词向量指向QKV矩阵的箭头
        for i in range(3):  # 三个矩阵
            start_point = word_vectors.get_bottom()
            end_point = qkv_matrices[i].get_top()
            arrow = Arrow(start_point, end_point, color=YELLOW, stroke_width=3)
            arrows.add(arrow)

        return arrows

    def create_stage3_attention_scores(self):
        """阶段3：计算注意力分数（点积）"""
        # 动作1：更新标题和步骤
        new_title = self.create_title_region_content("步骤3：计算注意力分数")
        new_step = self.create_step_region_content("向量点积计算")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作2：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据:", ["Q、K向量"])
        new_right_aux = self.create_right_auxiliary_content("衡量:", ["关联强度"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作3：显示分数矩阵和点积公式
        score_matrix = self.create_score_matrix()
        dot_product_formula = self.create_dot_product_formula()

        combined_group = VGroup(score_matrix, dot_product_formula)
        combined_group.arrange(DOWN, buff=0.8)

        new_main = self.create_main_region_content(combined_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 逐个填充矩阵元素的动画
        self.animate_score_matrix_filling(score_matrix)

        # 动作4：更新结果
        new_result = self.create_result_region_content("得到4x4原始分数矩阵")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

        self.wait(2)

    def create_score_matrix(self):
        """创建注意力分数矩阵"""
        # 4x4的注意力分数矩阵
        scores = [
            [2.1, 0.8, 1.2, 1.5],
            [0.9, 2.3, 0.7, 1.1],
            [1.4, 0.6, 2.0, 0.9],
            [1.7, 1.3, 0.8, 2.2]
        ]

        score_matrix = Matrix([[f"{val:.1f}" for val in row] for row in scores],
                             bracket_h_buff=0.1, bracket_v_buff=0.1,
                             element_to_mobject_config={"font_size": 18})
        score_matrix.set_color(ORANGE)

        return score_matrix

    def create_dot_product_formula(self):
        """创建点积公式"""
        formula = MathTex(r"q_i \cdot k_j = \sum_{d=1}^{d_k} q_{i,d} \times k_{j,d}",
                         font_size=28, color=WHITE)
        return formula

    def animate_score_matrix_filling(self, score_matrix):
        """动画显示矩阵填充过程"""
        # 获取矩阵的所有元素
        elements = score_matrix.get_entries()

        # 逐个显示元素
        for element in elements:
            self.play(Write(element), run_time=0.2)

        self.wait(1)

    def create_stage4_scaling_softmax(self):
        """阶段4：缩放与Softmax"""
        # 动作1：更新为缩放步骤
        new_title = self.create_title_region_content("步骤4：缩放")
        new_step = self.create_step_region_content("防止梯度消失")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作2：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据:", ["原始分数", "缩放因子"])
        new_right_aux = self.create_right_auxiliary_content("保持:", ["数值稳定"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作3：显示缩放公式和缩放过程
        scaling_formula = self.create_scaling_formula()
        scaled_matrix = self.create_scaled_matrix()

        combined_group = VGroup(scaled_matrix, scaling_formula)
        combined_group.arrange(DOWN, buff=0.8)

        new_main = self.create_main_region_content(combined_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        self.wait(1)

        # 动作4：更新为Softmax步骤
        new_title = self.create_title_region_content("步骤5：应用Softmax")
        new_step = self.create_step_region_content("归一化权重")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作5：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据:", ["缩放分数"])
        new_right_aux = self.create_right_auxiliary_content("生成:", ["概率分布"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作6：显示Softmax公式和注意力权重矩阵
        softmax_formula = self.create_softmax_formula()
        attention_matrix = self.create_attention_matrix()

        combined_group = VGroup(attention_matrix, softmax_formula)
        combined_group.arrange(DOWN, buff=0.8)

        new_main = self.create_main_region_content(combined_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 动作7：更新结果
        new_result = self.create_result_region_content("得到4x4注意力权重矩阵")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

        self.wait(2)

    def create_scaling_formula(self):
        """创建缩放公式"""
        formula = MathTex(r"\text{score}_{scaled} = \frac{\text{score}}{\sqrt{d_k}}",
                         font_size=28, color=WHITE)
        return formula

    def create_scaled_matrix(self):
        """创建缩放后的分数矩阵"""
        # 缩放后的分数（除以sqrt(3) ≈ 1.73）
        scaled_scores = [
            [1.2, 0.5, 0.7, 0.9],
            [0.5, 1.3, 0.4, 0.6],
            [0.8, 0.3, 1.2, 0.5],
            [1.0, 0.8, 0.5, 1.3]
        ]

        scaled_matrix = Matrix([[f"{val:.1f}" for val in row] for row in scaled_scores],
                              bracket_h_buff=0.1, bracket_v_buff=0.1,
                              element_to_mobject_config={"font_size": 18})
        scaled_matrix.set_color(YELLOW)

        return scaled_matrix

    def create_softmax_formula(self):
        """创建Softmax公式"""
        formula = MathTex(r"\text{softmax}(x_i) = \frac{e^{x_i}}{\sum_{j=1}^{n} e^{x_j}}",
                         font_size=28, color=WHITE)
        return formula

    def create_attention_matrix(self):
        """创建注意力权重矩阵"""
        # Softmax后的注意力权重（每行和为1）
        attention_weights = [
            [0.4, 0.2, 0.2, 0.2],
            [0.2, 0.5, 0.1, 0.2],
            [0.3, 0.1, 0.4, 0.2],
            [0.3, 0.3, 0.1, 0.3]
        ]

        attention_matrix = Matrix([[f"{val:.1f}" for val in row] for row in attention_weights],
                                 bracket_h_buff=0.1, bracket_v_buff=0.1,
                                 element_to_mobject_config={"font_size": 18})
        attention_matrix.set_color(GREEN)

        return attention_matrix

    def create_stage5_weighted_sum(self):
        """阶段5：加权求和"""
        # 动作1：更新标题和步骤
        new_title = self.create_title_region_content("步骤6：加权求和")
        new_step = self.create_step_region_content("输出上下文向量")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作2：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据:", ["权重矩阵", "V向量"])
        new_right_aux = self.create_right_auxiliary_content("综合:", ["语境信息"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作3：显示加权求和过程
        weighted_sum_formula = self.create_weighted_sum_formula()
        output_vectors = self.create_output_vectors()
        value_vectors = self.create_value_vectors()

        # 组合所有元素
        combined_group = VGroup(value_vectors, output_vectors, weighted_sum_formula)
        combined_group.arrange(DOWN, buff=0.6)

        new_main = self.create_main_region_content(combined_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 添加加权求和的箭头动画
        arrows = self.create_weighted_sum_arrows(value_vectors, output_vectors)
        self.play(Create(arrows))

        # 动作4：更新结果
        new_result = self.create_result_region_content("获得4个新的上下文向量")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

        self.wait(2)

    def create_weighted_sum_formula(self):
        """创建加权求和公式"""
        formula = MathTex(r"\text{output}_i = \sum_{j=1}^{n} \alpha_{ij} \cdot v_j",
                         font_size=28, color=WHITE)
        return formula

    def create_value_vectors(self):
        """创建Value向量组"""
        # 4个Value向量
        value_data = [
            [0.3, 0.7, 0.2],
            [0.8, 0.1, 0.6],
            [0.4, 0.9, 0.3],
            [0.6, 0.2, 0.8]
        ]

        value_vectors = []
        for i, vector in enumerate(value_data):
            v_matrix = Matrix([[f"{val:.1f}"] for val in vector],
                             bracket_h_buff=0.05, bracket_v_buff=0.05,
                             element_to_mobject_config={"font_size": 16})
            v_matrix.set_color(BLUE)

            label = MathTex(f"v_{i+1}", font_size=20, color=BLUE)
            v_group = VGroup(label, v_matrix)
            v_group.arrange(DOWN, buff=0.1)
            value_vectors.append(v_group)

        value_group = VGroup(*value_vectors)
        value_group.arrange(RIGHT, buff=0.4)

        return value_group

    def create_output_vectors(self):
        """创建输出上下文向量组"""
        # 4个输出向量
        output_data = [
            [0.5, 0.6, 0.4],
            [0.7, 0.3, 0.5],
            [0.4, 0.8, 0.3],
            [0.6, 0.4, 0.7]
        ]

        output_vectors = []
        for i, vector in enumerate(output_data):
            o_matrix = Matrix([[f"{val:.1f}"] for val in vector],
                             bracket_h_buff=0.05, bracket_v_buff=0.05,
                             element_to_mobject_config={"font_size": 16})
            o_matrix.set_color(PURPLE)

            label = MathTex(f"o_{i+1}", font_size=20, color=PURPLE)
            o_group = VGroup(label, o_matrix)
            o_group.arrange(DOWN, buff=0.1)
            output_vectors.append(o_group)

        output_group = VGroup(*output_vectors)
        output_group.arrange(RIGHT, buff=0.4)

        return output_group

    def create_weighted_sum_arrows(self, value_vectors, output_vectors):
        """创建加权求和的箭头"""
        arrows = VGroup()

        # 从每个Value向量指向对应的输出向量
        for i in range(4):
            start_point = value_vectors[i].get_bottom()
            end_point = output_vectors[i].get_top()
            arrow = Arrow(start_point, end_point, color=YELLOW, stroke_width=2)
            arrows.add(arrow)

        return arrows

    def create_stage6_application(self):
        """阶段6：实际应用"""
        # 动作1：更新标题和步骤
        new_title = self.create_title_region_content("步骤7：实际应用")
        new_step = self.create_step_region_content("下游任务落地")
        self.play(
            ReplacementTransform(self.region_elements['title'], new_title),
            ReplacementTransform(self.region_elements['step'], new_step)
        )
        self.region_elements['title'] = new_title
        self.region_elements['step'] = new_step

        # 动作2：更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("示例:", ["情感分析"])
        new_right_aux = self.create_right_auxiliary_content("示例:", ["机器翻译"])
        self.play(
            ReplacementTransform(self.region_elements['left_auxiliary'], new_left_aux),
            ReplacementTransform(self.region_elements['right_auxiliary'], new_right_aux)
        )
        self.region_elements['left_auxiliary'] = new_left_aux
        self.region_elements['right_auxiliary'] = new_right_aux

        # 动作3：显示情感分析示例
        sentiment_example = self.create_sentiment_analysis_example()
        new_main = self.create_main_region_content(sentiment_example)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        self.wait(2)

        # 切换到机器翻译示例
        translation_example = self.create_translation_example()
        new_main = self.create_main_region_content(translation_example)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 动作4：更新结果
        new_result = self.create_result_region_content("模型捕获长距离依赖")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

        self.wait(3)

    def create_sentiment_analysis_example(self):
        """创建情感分析示例"""
        # 输入文本
        input_text = Text("我爱吃苹果", font_size=24, color=WHITE)

        # 箭头
        arrow = Arrow(ORIGIN, RIGHT * 2, color=YELLOW)

        # 输出结果
        output_text = Text("积极", font_size=24, color=GREEN)
        output_box = SurroundingRectangle(output_text, color=GREEN, buff=0.2)
        output_group = VGroup(output_text, output_box)

        # 组合
        sentiment_group = VGroup(input_text, arrow, output_group)
        sentiment_group.arrange(RIGHT, buff=0.8)

        # 添加标题
        title = Text("情感分析", font_size=28, color=YELLOW)
        title.next_to(sentiment_group, UP, buff=0.5)

        example_group = VGroup(title, sentiment_group)

        return example_group

    def create_translation_example(self):
        """创建机器翻译示例"""
        # Encoder部分
        encoder_text = Text("我爱吃苹果", font_size=20, color=WHITE)
        encoder_box = SurroundingRectangle(encoder_text, color=BLUE, buff=0.2)
        encoder_label = Text("Encoder", font_size=16, color=BLUE)
        encoder_label.next_to(encoder_box, UP, buff=0.1)
        encoder_group = VGroup(encoder_text, encoder_box, encoder_label)

        # 箭头
        arrow = Arrow(ORIGIN, RIGHT * 2, color=YELLOW)

        # Decoder部分
        decoder_text = Text("I love apples", font_size=20, color=WHITE)
        decoder_box = SurroundingRectangle(decoder_text, color=RED, buff=0.2)
        decoder_label = Text("Decoder", font_size=16, color=RED)
        decoder_label.next_to(decoder_box, UP, buff=0.1)
        decoder_group = VGroup(decoder_text, decoder_box, decoder_label)

        # 组合
        translation_group = VGroup(encoder_group, arrow, decoder_group)
        translation_group.arrange(RIGHT, buff=0.8)

        # 添加标题
        title = Text("机器翻译", font_size=28, color=YELLOW)
        title.next_to(translation_group, UP, buff=0.5)

        example_group = VGroup(title, translation_group)

        return example_group


# 测试场景
if __name__ == "__main__":
    from manim import config
    config.media_width = "854"
    config.media_height = "480"
    config.frame_rate = 30
