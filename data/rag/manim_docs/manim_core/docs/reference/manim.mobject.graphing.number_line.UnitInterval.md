# UnitInterval

Qualified name: `manim.mobject.graphing.number\_line.UnitInterval`

### *class* UnitInterval(unit_size=10, numbers_with_elongated_ticks=None, decimal_number_config=None, \*\*kwargs)

Bases: [`NumberLine`](manim.mobject.graphing.number_line.NumberLine.md#manim.mobject.graphing.number_line.NumberLine)

### Methods

### Attributes

| `animate`             | Used to animate the application of any method of `self`.               |
|-----------------------|------------------------------------------------------------------------|
| `animation_overrides` |                                                                        |
| `color`               |                                                                        |
| `depth`               | The depth of the mobject.                                              |
| `fill_color`          | If there are multiple colors (for gradient) this returns the first one |
| `height`              | The height of the mobject.                                             |
| `n_points_per_curve`  |                                                                        |
| `sheen_factor`        |                                                                        |
| `stroke_color`        |                                                                        |
| `width`               | The width of the mobject.                                              |

#### \_original_\_init_\_(unit_size=10, numbers_with_elongated_ticks=None, decimal_number_config=None, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.
