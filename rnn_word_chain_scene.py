#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RNN算法 - 词语接龙高手场景
基于视觉故事板实现的完整Manim动画
"""

from manim import *
import numpy as np

# 颜色配置
COLOR_PRIMARY = "#2E8B57"      # 海绿色 - 主要元素
COLOR_SECONDARY = "#4169E1"     # 皇家蓝 - 次要元素  
COLOR_HIGHLIGHT = "#FF6347"     # 番茄红 - 高亮显示
COLOR_TEXT = "#2F4F4F"         # 深石板灰 - 文字
COLOR_BACKGROUND = "#F5F5DC"   # 米色 - 背景
COLOR_ACCENT = "#FFD700"       # 金色 - 强调色

class RNNWordChainScene(Scene):
    def construct(self):
        # 设置背景色
        self.camera.background_color = COLOR_BACKGROUND
        
        # 阶段1：引入词语接龙与AI选手
        self.stage_1_introduction()
        
        # 阶段2：输入词语序列
        self.stage_2_input_sequence()
        
        # 阶段3：处理第一个词
        self.stage_3_first_word()
        
        # 阶段4：处理第二个词
        self.stage_4_second_word()
        
        # 阶段5：处理第三个词
        self.stage_5_third_word()
        
        # 阶段6：预测下一个词
        self.stage_6_prediction()
        
        # 阶段7：实际应用 - 继续接龙
        self.stage_7_application()

    def create_title_region_content(self, title_text):
        """创建标题区域内容"""
        title = Text(title_text, font_size=48, color=COLOR_PRIMARY, weight=BOLD)
        title.to_edge(UP, buff=0.8)
        return title
    
    def create_step_region_content(self, step_text):
        """创建步骤区域内容"""
        step = Text(step_text, font_size=32, color=COLOR_SECONDARY)
        step.to_edge(UP, buff=2.0)
        return step
    
    def create_left_auxiliary_content(self, category, items):
        """创建左辅助区域内容"""
        if category is None:
            return VGroup()
        
        category_text = Text(category, font_size=24, color=COLOR_TEXT, weight=BOLD)
        item_texts = VGroup()
        for item in items:
            item_text = Text(item, font_size=20, color=COLOR_TEXT)
            item_texts.add(item_text)
        
        content = VGroup(category_text, item_texts).arrange(DOWN, aligned_edge=LEFT, buff=0.3)
        content.to_edge(LEFT, buff=0.5).shift(UP*1.5)
        return content
    
    def create_right_auxiliary_content(self, category, items):
        """创建右辅助区域内容"""
        if category is None:
            return VGroup()
        
        category_text = Text(category, font_size=24, color=COLOR_TEXT, weight=BOLD)
        item_texts = VGroup()
        for item in items:
            if item.startswith('$') and item.endswith('$'):
                item_text = MathTex(item[1:-1], font_size=20, color=COLOR_TEXT)
            else:
                item_text = Text(item, font_size=20, color=COLOR_TEXT)
            item_texts.add(item_text)
        
        content = VGroup(category_text, item_texts).arrange(DOWN, aligned_edge=LEFT, buff=0.3)
        content.to_edge(RIGHT, buff=0.5).shift(UP*1.5)
        return content
    
    def create_result_region_content(self, result_text):
        """创建结果区域内容"""
        if result_text is None:
            return VGroup()
        
        result = Text(result_text, font_size=24, color=COLOR_HIGHLIGHT)
        result.to_edge(DOWN, buff=0.8)
        return result
    
    def create_word_vector_group(self, words, vectors):
        """创建词向量组"""
        word_vector_group = VGroup()
        
        for i, (word, vector) in enumerate(zip(words, vectors)):
            # 创建词语文字
            word_text = Text(word, font_size=32, color=COLOR_TEXT)
            
            # 创建向量矩形框
            vector_rect = Rectangle(width=1.5, height=0.8, color=COLOR_PRIMARY, stroke_width=2)
            vector_text = Text(str(vector), font_size=18, color=COLOR_TEXT)
            vector_group = VGroup(vector_rect, vector_text)
            
            # 组合词语和向量
            word_vector = VGroup(word_text, vector_group).arrange(DOWN, buff=0.3)
            word_vector_group.add(word_vector)
        
        word_vector_group.arrange(RIGHT, buff=1.0)
        return word_vector_group
    
    def create_hidden_state_group(self, state_values, label="隐藏状态"):
        """创建隐藏状态组"""
        label_text = Text(label, font_size=20, color=COLOR_TEXT)
        state_rect = Rectangle(width=1.8, height=0.8, color=COLOR_SECONDARY, stroke_width=2)
        state_text = Text(str(state_values), font_size=18, color=COLOR_TEXT)
        
        state_group = VGroup(state_rect, state_text)
        hidden_state = VGroup(label_text, state_group).arrange(DOWN, buff=0.2)
        return hidden_state
    
    def create_formula_group_h(self):
        """创建隐藏状态计算公式组"""
        formula = MathTex(r"h_t = f(W_{xh}x_t + W_{hh}h_{t-1} + b_h)", font_size=28, color=COLOR_TEXT)
        formula_rect = SurroundingRectangle(formula, color=COLOR_ACCENT, stroke_width=2)
        return VGroup(formula_rect, formula)
    
    def create_formula_group_y(self):
        """创建输出计算公式组"""
        formula = MathTex(r"y_t = W_{hy}h_t + b_y", font_size=28, color=COLOR_TEXT)
        formula_rect = SurroundingRectangle(formula, color=COLOR_ACCENT, stroke_width=2)
        return VGroup(formula_rect, formula)
    
    def create_probability_table(self):
        """创建概率分布表格"""
        # 使用更简单的方式创建表格，避免复杂的Table类兼容性问题
        header_row = VGroup(
            Text("词语", font_size=20, color=COLOR_TEXT),
            Text("概率", font_size=20, color=COLOR_TEXT)
        ).arrange(RIGHT, buff=1.0)
        
        row1 = VGroup(
            Text("天安门", font_size=20, color=COLOR_HIGHLIGHT),
            Text("0.85", font_size=20, color=COLOR_HIGHLIGHT)
        ).arrange(RIGHT, buff=1.0)
        
        row2 = VGroup(
            Text("广场", font_size=20, color=COLOR_TEXT),
            Text("0.10", font_size=20, color=COLOR_TEXT)
        ).arrange(RIGHT, buff=1.0)
        
        row3 = VGroup(
            Text("其他", font_size=20, color=COLOR_TEXT),
            Text("0.05", font_size=20, color=COLOR_TEXT)
        ).arrange(RIGHT, buff=1.0)
        
        table = VGroup(header_row, row1, row2, row3).arrange(DOWN, buff=0.3)
        
        # 添加边框
        table_rect = SurroundingRectangle(table, color=COLOR_TEXT, stroke_width=1)
        
        return VGroup(table_rect, table)
    
    def create_ai_figure(self):
        """创建AI形象"""
        # 简化的AI头像
        head = Circle(radius=0.8, color=COLOR_PRIMARY, fill_opacity=0.3)
        eye_left = Circle(radius=0.1, color=COLOR_TEXT, fill_opacity=1).shift(LEFT*0.3 + UP*0.2)
        eye_right = Circle(radius=0.1, color=COLOR_TEXT, fill_opacity=1).shift(RIGHT*0.3 + UP*0.2)
        mouth = Arc(radius=0.3, start_angle=-PI/3, angle=PI/3, color=COLOR_TEXT, stroke_width=3).shift(DOWN*0.2)
        
        ai_figure = VGroup(head, eye_left, eye_right, mouth)
        return ai_figure
    
    def create_speech_bubble(self, text):
        """创建对话气泡"""
        bubble_text = Text(text, font_size=20, color=COLOR_TEXT)
        bubble_rect = RoundedRectangle(
            width=bubble_text.width + 0.8, 
            height=bubble_text.height + 0.4,
            corner_radius=0.2,
            color=COLOR_BACKGROUND,
            fill_opacity=0.9,
            stroke_color=COLOR_TEXT,
            stroke_width=2
        )
        
        # 简化气泡尾巴，避免复杂的定位问题
        tail = Text("▼", font_size=12, color=COLOR_TEXT)
        tail.next_to(bubble_rect, DOWN, buff=-0.1)
        
        return VGroup(bubble_rect, bubble_text, tail)

    def stage_1_introduction(self):
        """阶段1：引入词语接龙与AI选手"""
        # 创建标题
        title = self.create_title_region_content("词语接龙高手")
        self.play(FadeIn(title), run_time=1.5)
        self.wait(0.5)
        
        # 创建步骤介绍
        step_intro = self.create_step_region_content("AI选手大脑，有记忆力")
        self.play(FadeIn(step_intro), run_time=1)
        self.wait(0.5)
        
        # 创建AI形象
        ai_figure = self.create_ai_figure()
        ai_figure.center()
        self.play(FadeIn(ai_figure), run_time=1.5)
        self.wait(0.5)
        
        # 添加对话气泡
        speech_bubble = self.create_speech_bubble("记忆，就是RNN")
        speech_bubble.next_to(ai_figure, UP+RIGHT, buff=0.5)
        self.play(FadeIn(speech_bubble), run_time=1)
        self.wait(2)
        
        # 保存元素引用
        self.title = title
        self.current_step = step_intro
        self.ai_figure = ai_figure
        self.speech_bubble = speech_bubble

    def stage_2_input_sequence(self):
        """阶段2：输入词语序列"""
        # 更新步骤
        new_step = self.create_step_region_content("输入词语序列")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 移除AI形象和对话气泡
        self.play(FadeOut(self.ai_figure), FadeOut(self.speech_bubble), run_time=1)
        
        # 创建词向量组
        words = ["我", "爱", "北京"]
        vectors = ["[1,0,0]", "[0,1,0]", "[0,0,1]"]
        word_vector_group = self.create_word_vector_group(words, vectors)
        word_vector_group.center()
        
        # 先显示词语，再显示向量
        word_texts = VGroup(*[wv[0] for wv in word_vector_group])
        vector_groups = VGroup(*[wv[1] for wv in word_vector_group])
        
        self.play(FadeIn(word_texts), run_time=1.5)
        self.wait(0.5)
        self.play(FadeIn(vector_groups), run_time=1.5)
        self.wait(0.5)
        
        # 添加辅助信息
        left_aux = self.create_left_auxiliary_content("数据", ["词语序列: ['我', '爱', '北京']"])
        right_aux = self.create_right_auxiliary_content("辅助", ["词向量维度: 3"])
        
        self.play(FadeIn(left_aux), FadeIn(right_aux), run_time=1)
        self.wait(1)
        
        # 保存元素引用
        self.word_vector_group = word_vector_group
        self.left_aux = left_aux
        self.right_aux = right_aux

    def stage_3_first_word(self):
        """阶段3：处理第一个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第一个词")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据", ["当前词: ['我']", "隐藏状态: [0,0]"])
        new_right_aux = self.create_right_auxiliary_content("辅助", ["公式", r"$h_t = f(W_{xh}x_t + W_{hh}h_{t-1} + b_h)$"])
        
        self.play(
            Transform(self.left_aux, new_left_aux),
            Transform(self.right_aux, new_right_aux),
            run_time=1
        )
        self.wait(0.5)
        
        # 聚焦第一个词
        current_word = self.word_vector_group[0].copy()
        current_word.center().shift(LEFT*2)
        
        # 创建初始隐藏状态
        initial_hidden_state = self.create_hidden_state_group([0, 0], "初始状态")
        initial_hidden_state.next_to(current_word, UP, buff=1)
        
        # 创建公式
        formula_h = self.create_formula_group_h()
        formula_h.center()
        
        self.play(
            FadeOut(self.word_vector_group),
            FadeIn(current_word),
            FadeIn(initial_hidden_state),
            run_time=1.5
        )
        self.wait(0.5)
        
        self.play(FadeIn(formula_h), run_time=1)
        self.wait(0.5)
        
        # 添加箭头
        arrow1 = Arrow(current_word.get_right(), formula_h.get_left(), buff=0.1, color=COLOR_ACCENT)
        arrow2 = Arrow(initial_hidden_state.get_bottom(), formula_h.get_top(), buff=0.1, color=COLOR_ACCENT)
        
        self.play(GrowArrow(arrow1), GrowArrow(arrow2), run_time=1)
        self.wait(1)
        
        # 计算结果
        calculated_hidden_state = self.create_hidden_state_group([0.2, 0.4], "新状态")
        calculated_hidden_state.next_to(formula_h, RIGHT, buff=1)
        
        self.play(
            ReplacementTransform(initial_hidden_state, calculated_hidden_state),
            run_time=1.5
        )
        self.wait(0.5)
        
        # 显示结果
        result = self.create_result_region_content("新的隐藏状态: [0.2, 0.4]")
        self.play(FadeIn(result), run_time=1)
        self.wait(2)
        
        # 保存元素引用
        self.current_word = current_word
        self.formula_h = formula_h
        self.calculated_hidden_state = calculated_hidden_state
        self.arrows = VGroup(arrow1, arrow2)
        self.result = result

    def stage_4_second_word(self):
        """阶段4：处理第二个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第二个词")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 清理上一阶段的结果
        self.play(FadeOut(self.result), run_time=0.5)
        
        # 更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据", ["当前词: ['爱']", "上一状态: [0.2,0.4]"])
        self.play(Transform(self.left_aux, new_left_aux), run_time=1)
        self.wait(0.5)
        
        # 更新当前词为"爱"
        second_word = self.create_word_vector_group(["爱"], ["[0,1,0]"])[0]
        second_word.move_to(self.current_word)
        
        self.play(ReplacementTransform(self.current_word, second_word), run_time=1)
        self.wait(0.5)
        
        # 更新隐藏状态为上一步的结果
        prev_hidden_state = self.create_hidden_state_group([0.2, 0.4], "上一状态")
        prev_hidden_state.move_to(self.calculated_hidden_state)
        
        self.play(ReplacementTransform(self.calculated_hidden_state, prev_hidden_state), run_time=1)
        self.wait(0.5)
        
        # 重新计算
        new_calculated_hidden_state = self.create_hidden_state_group([0.6, 0.8], "新状态")
        new_calculated_hidden_state.next_to(self.formula_h, RIGHT, buff=1)
        
        self.play(ReplacementTransform(prev_hidden_state, new_calculated_hidden_state), run_time=1.5)
        self.wait(0.5)
        
        # 显示结果
        result = self.create_result_region_content('更新后状态: [0.6, 0.8] (包含"我爱"信息)')
        self.play(FadeIn(result), run_time=1)
        self.wait(2)
        
        # 更新引用
        self.current_word = second_word
        self.calculated_hidden_state = new_calculated_hidden_state
        self.result = result

    def stage_5_third_word(self):
        """阶段5：处理第三个词"""
        # 更新步骤
        new_step = self.create_step_region_content("处理第三个词")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 清理上一阶段的结果
        self.play(FadeOut(self.result), run_time=0.5)
        
        # 更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据", ["当前词: ['北京']", "上一状态: [0.6,0.8]"])
        self.play(Transform(self.left_aux, new_left_aux), run_time=1)
        self.wait(0.5)
        
        # 更新当前词为"北京"
        third_word = self.create_word_vector_group(["北京"], ["[0,0,1]"])[0]
        third_word.move_to(self.current_word)
        
        self.play(ReplacementTransform(self.current_word, third_word), run_time=1)
        self.wait(0.5)
        
        # 更新隐藏状态为上一步的结果
        prev_hidden_state = self.create_hidden_state_group([0.6, 0.8], "上一状态")
        prev_hidden_state.move_to(self.calculated_hidden_state)
        
        self.play(ReplacementTransform(self.calculated_hidden_state, prev_hidden_state), run_time=1)
        self.wait(0.5)
        
        # 最终计算
        final_hidden_state = self.create_hidden_state_group([0.9, 0.7], "最终状态")
        final_hidden_state.next_to(self.formula_h, RIGHT, buff=1)
        
        self.play(ReplacementTransform(prev_hidden_state, final_hidden_state), run_time=1.5)
        self.wait(0.5)
        
        # 显示结果
        result = self.create_result_region_content('最终状态: [0.9, 0.7] (理解"我爱北京")')
        self.play(FadeIn(result), run_time=1)
        self.wait(2)
        
        # 更新引用
        self.current_word = third_word
        self.final_hidden_state = final_hidden_state
        self.result = result

    def stage_6_prediction(self):
        """阶段6：预测下一个词"""
        # 更新步骤
        new_step = self.create_step_region_content("预测下一个词")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 清理上一阶段的元素
        self.play(
            FadeOut(self.result),
            FadeOut(self.current_word),
            FadeOut(self.formula_h),
            FadeOut(self.arrows),
            run_time=1
        )
        
        # 更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据", ["最终状态: [0.9,0.7]"])
        new_right_aux = self.create_right_auxiliary_content("辅助", ["公式", r"$y_t = W_{hy}h_t + b_y$"])
        
        self.play(
            Transform(self.left_aux, new_left_aux),
            Transform(self.right_aux, new_right_aux),
            run_time=1
        )
        self.wait(0.5)
        
        # 移动最终隐藏状态到中心左侧
        self.final_hidden_state.center().shift(LEFT*2.5)
        self.wait(0.5)
        
        # 创建输出公式
        formula_y = self.create_formula_group_y()
        formula_y.center()
        
        self.play(FadeIn(formula_y), run_time=1)
        self.wait(0.5)
        
        # 添加箭头
        arrow_to_y = Arrow(self.final_hidden_state.get_right(), formula_y.get_left(), buff=0.1, color=COLOR_ACCENT)
        self.play(GrowArrow(arrow_to_y), run_time=1)
        self.wait(1)
        
        # 显示概率表格
        probability_table = self.create_probability_table()
        probability_table.next_to(formula_y, RIGHT, buff=1)
        
        self.play(
            FadeOut(formula_y),
            FadeOut(arrow_to_y),
            FadeIn(probability_table),
            run_time=1.5
        )
        self.wait(1)
        
        # 显示结果
        result = self.create_result_region_content('预测下一个词："天安门" (概率最高)')
        self.play(FadeIn(result), run_time=1)
        self.wait(2)
        
        # 保存引用
        self.probability_table = probability_table
        self.result = result

    def stage_7_application(self):
        """阶段7：实际应用 - 继续接龙"""
        # 更新步骤
        new_step = self.create_step_region_content("实际应用 - 继续接龙")
        self.play(Transform(self.current_step, new_step), run_time=1)
        self.wait(0.5)
        
        # 清理屏幕
        self.play(
            FadeOut(self.result),
            FadeOut(self.final_hidden_state),
            FadeOut(self.probability_table),
            FadeOut(self.right_aux),
            run_time=1
        )
        
        # 更新辅助信息
        new_left_aux = self.create_left_auxiliary_content("数据", ["输入序列: ['我', '爱', '北京']"])
        self.play(Transform(self.left_aux, new_left_aux), run_time=1)
        self.wait(0.5)
        
        # 重新显示原始词语序列
        words = ["我", "爱", "北京"]
        word_texts = VGroup()
        for word in words:
            word_text = Text(word, font_size=40, color=COLOR_TEXT)
            word_texts.add(word_text)
        
        word_texts.arrange(RIGHT, buff=1.0).center()
        self.play(FadeIn(word_texts), run_time=1.5)
        self.wait(0.5)
        
        # 添加预测的词"天安门"
        predicted_word = Text("天安门", font_size=40, color=COLOR_HIGHLIGHT)
        predicted_word.next_to(word_texts, RIGHT, buff=1.0)
        
        self.play(FadeIn(predicted_word), run_time=1)
        self.wait(0.5)
        
        # 重新组织序列
        current_sequence = VGroup(word_texts, predicted_word)
        current_sequence.arrange(RIGHT, buff=0.8).center()
        self.wait(0.5)
        
        # 添加AI形象和循环箭头
        ai_figure_small = self.create_ai_figure().scale(0.6)
        ai_figure_small.to_corner(DR)
        
        # 创建简单的循环指示符，避免CurvedArrow兼容性问题
        loop_text = Text("🔄", font_size=24, color=COLOR_ACCENT)
        loop_text.next_to(ai_figure_small, UP, buff=0.1)
        
        self.play(FadeIn(ai_figure_small), FadeIn(loop_text), run_time=1.5)
        self.wait(1)
        
        # 最终结果
        final_result = self.create_result_region_content("AI流畅接龙，展现对语言序列的理解和生成能力。")
        self.play(FadeIn(final_result), run_time=1)
        self.wait(3)
        
        # 结束动画
        self.play(FadeOut(Group(*self.mobjects)), run_time=2)
        self.wait(1)

# 配置场景
if __name__ == "__main__":
    config.background_color = COLOR_BACKGROUND
    config.pixel_height = 1080
    config.pixel_width = 1920
    config.frame_rate = 30 