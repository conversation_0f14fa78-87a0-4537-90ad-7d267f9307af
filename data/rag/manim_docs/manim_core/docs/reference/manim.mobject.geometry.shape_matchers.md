# shape_matchers

Mobjects used to mark and annotate other mobjects.

### Classes

| [`BackgroundRectangle`](manim.mobject.geometry.shape_matchers.BackgroundRectangle.md#manim.mobject.geometry.shape_matchers.BackgroundRectangle)    | A background rectangle.                                                                               |
|----------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|
| [`Cross`](manim.mobject.geometry.shape_matchers.Cross.md#manim.mobject.geometry.shape_matchers.Cross)                                              | Creates a cross.                                                                                      |
| [`SurroundingRectangle`](manim.mobject.geometry.shape_matchers.SurroundingRectangle.md#manim.mobject.geometry.shape_matchers.SurroundingRectangle) | A rectangle surrounding a [`Mobject`](manim.mobject.mobject.Mobject.md#manim.mobject.mobject.Mobject) |
| [`Underline`](manim.mobject.geometry.shape_matchers.Underline.md#manim.mobject.geometry.shape_matchers.Underline)                                  | Creates an underline.                                                                                 |
