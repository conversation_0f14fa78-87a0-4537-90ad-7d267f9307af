# numbers

Mobjects representing numbers.

### Classes

| [`DecimalNumber`](manim.mobject.text.numbers.DecimalNumber.md#manim.mobject.text.numbers.DecimalNumber)   | An mobject representing a decimal number.                                                                                                                                                                 |
|-----------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`Integer`](manim.mobject.text.numbers.Integer.md#manim.mobject.text.numbers.Integer)                     | A class for displaying Integers.                                                                                                                                                                          |
| [`Variable`](manim.mobject.text.numbers.Variable.md#manim.mobject.text.numbers.Variable)                  | A class for displaying text that shows "label = value" with the value continuously updated from a [`ValueTracker`](manim.mobject.value_tracker.ValueTracker.md#manim.mobject.value_tracker.ValueTracker). |
