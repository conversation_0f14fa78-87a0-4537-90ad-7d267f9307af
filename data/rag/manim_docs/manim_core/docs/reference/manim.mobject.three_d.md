# three_d

Three-dimensional mobjects.

## Modules

| [`polyhedra`](manim.mobject.three_d.polyhedra.md#module-manim.mobject.three_d.polyhedra)                      | General polyhedral class and platonic solids.     |
|---------------------------------------------------------------------------------------------------------------|---------------------------------------------------|
| [`three_d_utils`](manim.mobject.three_d.three_d_utils.md#module-manim.mobject.three_d.three_d_utils)          | Utility functions for three-dimensional mobjects. |
| [`three_dimensions`](manim.mobject.three_d.three_dimensions.md#module-manim.mobject.three_d.three_dimensions) | Three-dimensional mobjects.                       |
