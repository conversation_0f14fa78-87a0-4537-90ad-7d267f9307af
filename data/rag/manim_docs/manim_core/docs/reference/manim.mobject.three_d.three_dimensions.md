# three_dimensions

Three-dimensional mobjects.

### Classes

| [`Arrow3D`](manim.mobject.three_d.three_dimensions.Arrow3D.md#manim.mobject.three_d.three_dimensions.Arrow3D)                      | An arrow made out of a cylindrical line and a conical tip.   |
|------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------|
| [`Cone`](manim.mobject.three_d.three_dimensions.Cone.md#manim.mobject.three_d.three_dimensions.Cone)                               | A circular cone.                                             |
| [`Cube`](manim.mobject.three_d.three_dimensions.Cube.md#manim.mobject.three_d.three_dimensions.Cube)                               | A three-dimensional cube.                                    |
| [`Cylinder`](manim.mobject.three_d.three_dimensions.Cylinder.md#manim.mobject.three_d.three_dimensions.Cylinder)                   | A cylinder, defined by its height, radius and direction,     |
| [`Dot3D`](manim.mobject.three_d.three_dimensions.Dot3D.md#manim.mobject.three_d.three_dimensions.Dot3D)                            | A spherical dot.                                             |
| [`Line3D`](manim.mobject.three_d.three_dimensions.Line3D.md#manim.mobject.three_d.three_dimensions.Line3D)                         | A cylindrical line, for use in ThreeDScene.                  |
| [`Prism`](manim.mobject.three_d.three_dimensions.Prism.md#manim.mobject.three_d.three_dimensions.Prism)                            | A right rectangular prism (or rectangular cuboid).           |
| [`Sphere`](manim.mobject.three_d.three_dimensions.Sphere.md#manim.mobject.three_d.three_dimensions.Sphere)                         | A three-dimensional sphere.                                  |
| [`Surface`](manim.mobject.three_d.three_dimensions.Surface.md#manim.mobject.three_d.three_dimensions.Surface)                      | Creates a Parametric Surface using a checkerboard pattern.   |
| [`ThreeDVMobject`](manim.mobject.three_d.three_dimensions.ThreeDVMobject.md#manim.mobject.three_d.three_dimensions.ThreeDVMobject) |                                                              |
| [`Torus`](manim.mobject.three_d.three_dimensions.Torus.md#manim.mobject.three_d.three_dimensions.Torus)                            | A torus.                                                     |
