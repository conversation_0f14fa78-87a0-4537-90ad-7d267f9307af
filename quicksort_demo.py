from manim import *
import random
from prompts.professional_science_template import ProfessionalScienceTemplate


class QuickSortDemo(ProfessionalScienceTemplate):
    """
    快速排序算法可视化演示
    
    演示内容：
    1. 初始数组展示
    2. 选择基准值
    3. 分区操作
    4. 递归排序
    5. 最终结果
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始数组
        self.array = [64, 34, 25, 12, 22, 11, 90]
        self.array_visual = None
        self.array_elements = []  # 存储单个数组元素的引用
        self.pivot_indicator = None
        
        # 动画配置
        self.animation_config = {
            'element_move_time': 1.0,
            'color_change_time': 0.5,
            'fade_time': 0.3,
            'highlight_time': 0.5,
            'swap_time': 1.2
        }
        
    def construct(self):
        # 设置背景
        self.setup_background()
        
        # 创建标题（固定显示）
        title = self.create_title_region_content("快速排序")
        self.add(title)
        
        # 步骤1：初始化展示
        self.show_initialization()
        self.wait(2)
        
        # 步骤2：选择基准值
        self.show_pivot_selection()
        self.wait(2)
        
        # 步骤3：分区操作
        self.show_partition_process()
        self.wait(2)
        
        # 步骤4：递归排序演示
        self.show_recursive_sorting()
        self.wait(2)
        
        # 步骤5：最终结果
        self.show_final_result()
        self.wait(3)
    
    def create_array_visualization(self, array, highlight_indices=None, pivot_index=None, store_elements=False):
        """创建数组可视化"""
        elements = VGroup()
        element_list = []
        
        for i, value in enumerate(array):
            # 创建矩形框
            rect = Rectangle(
                width=0.8,
                height=0.8,
                stroke_color=WHITE,
                stroke_width=2,
                fill_opacity=0.3
            )
            
            # 根据不同状态设置颜色
            if pivot_index is not None and i == pivot_index:
                rect.set_fill(color=RED, opacity=0.7)  # 基准值用红色
            elif highlight_indices and i in highlight_indices:
                rect.set_fill(color=YELLOW, opacity=0.7)  # 高亮用黄色
            else:
                rect.set_fill(color=BLUE, opacity=0.3)  # 普通元素用蓝色
            
            # 创建数值文字
            text = Text(
                str(value),
                font_size=24,
                color=WHITE,
                weight=BOLD
            )
            
            # 组合矩形和文字
            element = VGroup(rect, text)
            # 给元素添加标识
            element.value = value
            element.original_index = i
            elements.add(element)
            element_list.append(element)
        
        # 排列数组元素
        elements.arrange(RIGHT, buff=0.1)
        
        if store_elements:
            self.array_elements = element_list
        
        return elements
    
    def update_element_color(self, element, color, opacity=0.7):
        """更新单个元素的颜色"""
        rect = element[0]  # 矩形是第一个子对象
        return rect.animate.set_fill(color=color, opacity=opacity)
    
    def highlight_elements(self, elements, color=YELLOW, duration=None):
        """高亮指定的元素"""
        if duration is None:
            duration = self.animation_config['highlight_time']
        
        animations = []
        for element in elements:
            animations.append(self.update_element_color(element, color))
        
        return AnimationGroup(*animations, run_time=duration)
    
    def move_elements_to_positions(self, elements, target_positions, duration=None):
        """将元素移动到指定位置"""
        if duration is None:
            duration = self.animation_config['element_move_time']
            
        animations = []
        for element, target_pos in zip(elements, target_positions):
            animations.append(element.animate.move_to(target_pos))
        
        return AnimationGroup(*animations, run_time=duration)
    
    def swap_elements_animation(self, element1, element2, duration=None):
        """交换两个元素的动画"""
        if duration is None:
            duration = self.animation_config['swap_time']
        
        # 获取当前位置
        pos1 = element1.get_center()
        pos2 = element2.get_center()
        
        # 创建交换动画
        return AnimationGroup(
            element1.animate.move_to(pos2),
            element2.animate.move_to(pos1),
            run_time=duration
        )
    
    def show_initialization(self):
        """步骤1：初始化展示"""
        # 更新步骤描述
        step = self.create_step_region_content("步骤1：初始化")
        
        # 创建初始数组可视化（存储元素引用）
        self.array_visual = self.create_array_visualization(self.array, store_elements=True)
        self.array_label = Text("待排序数组:", font_size=20, color=WHITE).next_to(self.array_visual, UP, buff=0.5)
        main_content = VGroup(self.array_label, self.array_visual)
        main = self.create_main_region_content(main_content)
        
        # 左侧辅助：算法特点
        left_aux = self.create_left_auxiliary_content("算法特点:", [
            "• 分治策略",
            "• 原地排序", 
            "• 不稳定排序",
            "• 递归实现",
            "• 效率较高"
        ])
        
        # 右侧辅助：复杂度分析
        right_aux = self.create_right_auxiliary_content("复杂度:", [
            "平均时间:",
            MathTex(r"O(n \log n)", font_size=20),
            "最坏时间:",
            MathTex(r"O(n^2)", font_size=20),
            "空间复杂度:",
            MathTex(r"O(\log n)", font_size=20)
        ])
        
        # 结果描述
        result = self.create_result_region_content("初始化：创建包含7个元素的无序数组")
        
        # 动画展示
        self.play(Write(step))
        self.play(FadeIn(main))
        self.play(FadeIn(left_aux), FadeIn(right_aux))
        self.play(Write(result))
        
        # 保存当前状态
        self.current_step = step
        self.current_main = main
        self.current_left = left_aux
        self.current_right = right_aux
        self.current_result = result
    
    def show_pivot_selection(self):
        """步骤2：选择基准值"""
        # 选择最后一个元素作为基准值
        pivot_index = len(self.array) - 1
        pivot_value = self.array[pivot_index]
        pivot_element = self.array_elements[pivot_index]
        
        # 步骤转换动画
        new_step = self.create_step_region_content("步骤2：选择基准")
        
        # 更新数组标签
        new_array_label = Text("选择基准值(最后一个元素):", font_size=20, color=WHITE).next_to(self.array_visual, UP, buff=0.5)
        
        # 添加基准值说明
        pivot_label = Text(
            f"基准值: {pivot_value}",
            font_size=20,
            color=RED,
            weight=BOLD
        ).next_to(self.array_visual, DOWN, buff=0.5)
        
        # 更新左侧辅助：基准选择策略
        new_left = self.create_left_auxiliary_content("基准选择:", [
            "• 选择最后元素",
            "• 简单易实现",
            "• 标记为红色",
            "• 作为分界点",
            "• 分区参考值"
        ])
        
        # 新结果
        new_result = self.create_result_region_content(f"选择基准值：{pivot_value}，准备开始分区操作")
        
        # 执行精细动画
        self.play(
            # 步骤和辅助区域的变化
            Transform(self.current_step, new_step),
            Transform(self.current_left, new_left),
            Transform(self.current_result, new_result),
            # 标签的变化
            Transform(self.array_label, new_array_label),
            # 基准值高亮动画
            self.update_element_color(pivot_element, RED),
            run_time=1.0
        )
        
        # 添加基准值标签
        self.play(FadeIn(pivot_label))
        
        # 更新状态
        self.current_step = new_step
        self.current_left = new_left
        self.current_result = new_result
        self.array_label = new_array_label
        self.pivot_label = pivot_label
    
    def show_partition_process(self):
        """步骤3：分区操作"""
        new_step = self.create_step_region_content("步骤3：分区操作")
        
        # 模拟分区过程
        pivot_value = self.array[-1]  # 90
        left_part = [x for x in self.array[:-1] if x <= pivot_value]  # [64,34,25,12,22,11]
        right_part = [x for x in self.array[:-1] if x > pivot_value]  # []
        
        # 更新数组标签
        new_array_label = Text("分区结果:", font_size=20, color=WHITE).next_to(self.array_visual, UP, buff=0.5)
        
        # 创建分区标签
        left_label = Text("≤ 基准值", font_size=16, color=BLUE)
        pivot_label_new = Text("基准值", font_size=16, color=RED)
        
        # 更新左侧辅助：分区过程
        new_left = self.create_left_auxiliary_content("分区过程:", [
            "• 遍历数组元素",
            "• 与基准值比较",
            "• 小的放左边",
            "• 大的放右边",
            "• 基准值就位"
        ])
        
        # 右侧辅助：分区结果
        new_right = self.create_right_auxiliary_content("分区结果:", [
            f"左分区: {len(left_part)}个",
            f"基准值: {pivot_value}",
            f"右分区: {len(right_part)}个",
            "基准值已就位",
            "准备递归排序"
        ])
        
        new_result = self.create_result_region_content("分区完成：基准值90已就位，左右分区待排序")
        
        # 步骤1：更新步骤和辅助区域
        self.play(
            Transform(self.current_step, new_step),
            Transform(self.current_left, new_left),
            Transform(self.current_right, new_right),
            Transform(self.current_result, new_result),
            Transform(self.array_label, new_array_label),
            FadeOut(self.pivot_label),
            run_time=0.8
        )
        
        # 步骤2：高亮需要比较的元素（排除基准值）
        elements_to_compare = self.array_elements[:-1]  # 除了最后一个基准值
        self.play(
            self.highlight_elements(elements_to_compare, YELLOW),
            run_time=0.5
        )
        self.wait(0.5)
        
        # 步骤3：分区动画 - 由于所有元素都≤基准值，只需要变色
        left_elements = self.array_elements[:-1]  # 前6个元素
        pivot_element = self.array_elements[-1]   # 基准值元素
        
        # 将左分区元素变为蓝色
        self.play(
            self.highlight_elements(left_elements, BLUE),
            run_time=0.8
        )
        
        # 步骤4：添加分区标签
        left_label.next_to(self.array_visual[0:6], DOWN, buff=0.3)
        pivot_label_new.next_to(self.array_visual[6], DOWN, buff=0.3)
        
        self.play(
            FadeIn(left_label),
            FadeIn(pivot_label_new),
            run_time=0.5
        )
        
        # 更新状态和数组（实际没有移动，只是颜色变化）
        self.array = left_part + [pivot_value] + right_part
        self.current_step = new_step
        self.current_left = new_left
        self.current_right = new_right
        self.current_result = new_result
        self.array_label = new_array_label
        self.partition_labels = VGroup(left_label, pivot_label_new)
    
    def show_recursive_sorting(self):
        """步骤4：递归排序演示"""
        new_step = self.create_step_region_content("步骤4：递归排序")
        
        # 对左分区[64,34,25,12,22,11]进行快速排序
        left_part = [64, 34, 25, 12, 22, 11]
        sorted_left = sorted(left_part)  # [11,12,22,25,34,64]
        final_array = sorted_left + [90]  # [11,12,22,25,34,64,90]
        
        # 更新数组标签
        new_array_label = Text("递归排序左分区:", font_size=20, color=WHITE).next_to(self.array_visual, UP, buff=0.5)
        
        # 更新左侧辅助：递归过程
        new_left = self.create_left_auxiliary_content("递归过程:", [
            "• 左分区递归排序",
            "• 选择新基准值",
            "• 重复分区操作",
            "• 直到子数组≤1",
            "• 合并有序结果"
        ])
        
        # 更新右侧辅助：递归深度
        new_right = self.create_right_auxiliary_content("递归信息:", [
            "递归深度: 3层",
            "左分区: 已排序",
            "右分区: 无元素",
            "基准值: 已就位",
            "排序接近完成"
        ])
        
        new_result = self.create_result_region_content("递归排序：左分区已完成排序，整体排序即将完成")
        
        # 步骤1：更新步骤和辅助区域
        self.play(
            Transform(self.current_step, new_step),
            Transform(self.current_left, new_left),
            Transform(self.current_right, new_right),
            Transform(self.current_result, new_result),
            Transform(self.array_label, new_array_label),
            FadeOut(self.partition_labels),
            run_time=0.8
        )
        
        # 步骤2：高亮左分区准备排序
        left_elements = self.array_elements[:-1]  # 前6个元素
        self.play(
            self.highlight_elements(left_elements, ORANGE),
            run_time=0.5
        )
        self.wait(0.5)
        
        # 步骤3：元素级排序动画
        # 创建映射：当前位置到目标位置
        current_values = [64, 34, 25, 12, 22, 11]
        target_values = [11, 12, 22, 25, 34, 64]
        
        # 找到每个元素需要移动到的位置
        target_positions = []
        for i, element in enumerate(self.array_elements[:-1]):
            current_value = element.value
            target_index = target_values.index(current_value)
            target_pos = self.array_elements[target_index].get_center()
            target_positions.append(target_pos)
        
        # 执行排序动画
        self.play(
            self.move_elements_to_positions(left_elements, target_positions, duration=1.5),
            run_time=1.5
        )
        
        # 步骤4：更新元素颜色表示排序完成
        self.play(
            self.highlight_elements(left_elements, GREEN),
            run_time=0.5
        )
        
        # 步骤5：添加排序完成说明
        process_label = Text(
            "[64,34,25,12,22,11] → [11,12,22,25,34,64]",
            font_size=16,
            color=YELLOW
        ).next_to(self.array_visual, DOWN, buff=0.5)
        
        self.play(FadeIn(process_label))
        
        # 更新状态
        self.array = final_array
        # 重新排序元素引用以匹配新的位置
        new_element_order = []
        for value in target_values:
            for element in self.array_elements[:-1]:
                if element.value == value:
                    new_element_order.append(element)
                    break
        new_element_order.append(self.array_elements[-1])  # 添加基准值元素
        self.array_elements = new_element_order
        
        self.current_step = new_step
        self.current_left = new_left
        self.current_right = new_right
        self.current_result = new_result
        self.array_label = new_array_label
        self.process_label = process_label
    
    def show_final_result(self):
        """步骤5：最终结果"""
        new_step = self.create_step_region_content("步骤5：排序完成")
        
        # 更新数组标签
        new_array_label = Text("✓ 排序完成!", font_size=24, color=GREEN, weight=BOLD).next_to(self.array_visual, UP, buff=0.5)
        
        # 更新左侧辅助：算法总结
        new_left = self.create_left_auxiliary_content("算法总结:", [
            "• 分治策略有效",
            "• 基准值选择",
            "• 分区操作核心",
            "• 递归排序",
            "• 效率较高"
        ])
        
        # 更新右侧辅助：性能表现
        new_right = self.create_right_auxiliary_content("性能表现:", [
            "原数组: 7个元素",
            "比较次数: ~15次",
            "递归深度: 3层",
            "排序时间: 优秀",
            "内存使用: 较少"
        ])
        
        new_result = self.create_result_region_content("快速排序完成！数组[64,34,25,12,22,11,90]已升序排列")
        
        # 步骤1：更新步骤和辅助区域
        self.play(
            Transform(self.current_step, new_step),
            Transform(self.current_left, new_left),
            Transform(self.current_right, new_right),
            Transform(self.current_result, new_result),
            Transform(self.array_label, new_array_label),
            FadeOut(self.process_label),
            run_time=0.8
        )
        
        # 步骤2：将所有元素变为成功的绿色
        all_elements = self.array_elements
        self.play(
            self.highlight_elements(all_elements, GREEN, duration=1.0),
            run_time=1.0
        )
        
        # 步骤3：添加成功标识
        success_icon = Text("✓", font_size=32, color=GREEN, weight=BOLD).next_to(self.array_visual, DOWN, buff=0.3)
        completion_text = Text("从 [64,34,25,12,22,11,90] 到 [11,12,22,25,34,64,90]", 
                              font_size=16, color=WHITE).next_to(success_icon, DOWN, buff=0.2)
        
        # 先设置success_elements属性
        self.success_elements = VGroup(success_icon, completion_text)
        
        self.play(
            FadeIn(success_icon),
            FadeIn(completion_text),
            run_time=0.8
        )
        
        # 步骤4：添加庆祝效果
        self.add_celebration_effect()
        
        # 步骤5：优雅的退场动画
        self.add_exit_animation()
        
        # 更新状态
        self.current_step = new_step
        self.current_left = new_left
        self.current_right = new_right
        self.current_result = new_result
        self.array_label = new_array_label
    
    def add_celebration_effect(self):
        """添加庆祝效果"""
        # 创建一些闪烁的星星
        stars = VGroup()
        colors = [YELLOW, GOLD, ORANGE, "#FFD700", "#FFA500"]
        
        for _ in range(12):
            star = Text("★", font_size=20, color=random.choice(colors))
            star.move_to([
                random.uniform(-5, 5),
                random.uniform(-2.5, 2.5),
                0
            ])
            stars.add(star)
        
        # 星星闪烁动画
        self.play(
            LaggedStart(*[FadeIn(star, scale_factor=2) for star in stars], lag_ratio=0.08),
            run_time=1.0
        )
        self.wait(0.5)
        self.play(
            LaggedStart(*[FadeOut(star, scale_factor=0.5) for star in stars], lag_ratio=0.05),
            run_time=0.8
        )
    
    def add_exit_animation(self):
        """添加优雅的退场动画"""
        # 收集所有当前可见的元素
        all_visible_elements = VGroup(
            self.current_step,
            self.current_left,
            self.current_right, 
            self.current_result,
            self.array_label,
            self.array_visual,
            self.success_elements
        )
        
        # 分层退场动画
        # 第1层：辅助区域淡出
        self.play(
            FadeOut(self.current_left, shift=LEFT),
            FadeOut(self.current_right, shift=RIGHT),
            run_time=0.8
        )
        
        # 第2层：步骤和结果区域滑出
        self.play(
            FadeOut(self.current_step, shift=UP),
            FadeOut(self.current_result, shift=DOWN),
            run_time=0.8
        )
        
        # 第3层：数组元素逐个消失
        self.play(
            LaggedStart(*[
                FadeOut(element, scale_factor=0.3) 
                for element in self.array_elements
            ], lag_ratio=0.1),
            run_time=1.2
        )
        
        # 第4层：标签和成功标识
        self.play(
            FadeOut(self.array_label, shift=UP),
            FadeOut(self.success_elements, shift=DOWN),
            run_time=0.8
        )
        
        # 最终暂停
        self.wait(1)


# 如果直接运行此文件
if __name__ == "__main__":
    # 可以在此添加测试代码
    print("快速排序演示类已定义完成！")
    print("使用方法：manim quicksort_demo.py QuickSortDemo") 