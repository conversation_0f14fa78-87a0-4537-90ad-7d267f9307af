专业科学模板使用指南：

1. 继承ProfessionalScienceTemplate类
2. 重写construct方法
3. 使用以下标准接口方法创建各区域内容：
   
   - self.create_title_region_content(title): 创建标题区域
   - self.create_step_region_content(step): 创建步骤区域
   - self.create_main_region_content(content): 创建主内容区域
   - self.create_left_auxiliary_content(title, items): 创建左辅助区域
   - self.create_right_auxiliary_content(title, items): 创建右辅助区域
   - self.create_result_region_content(result): 创建结果区域

   - **重要**同一个区域元素必须先用VGroup封装，并调用接口创建，禁止单独创建元素
   - **重要**同一个区域元素切换必须使用ReplacementTransform函数

4. 📝 内容建议指南（确保最佳效果）：
   
   区域类型 | 建议限制 | 示例
   --------|---------|-------
   标题区域 | 8个字以内 | "数学原理"、"物理定律"
   步骤区域 | 12个字以内 | "第一步：数据预处理"
   辅助标题 | 6个字以内 | "要点"、"公式"、"特点"
   辅助项目 | 5项×15字/项 | "• 开口向上"、"• 时间复杂度O(n²)"
   结果区域 | 40个字以内 | "结论：二次函数具有抛物线形状..."

5. 快速开始模板：
    ```python
    from prompts.professional_science_template import ProfessionalScienceTemplate
    
    class MyExample(ProfessionalScienceTemplate):
        def construct(self):
            # 必须调用
            self.setup_background()

            # 阶段一内容
            self.create_stage1_content()

            # 阶段二内容
            self.create_stage1_content()

        def create_stage1_content(self)
            # 基本布局
            # 标题区域
            if self.region_elements["title"] is not None:
                self.play(ReplacementTransform(self.region_elements['title'], title_group))
                self.region_elements['title'] = title_group
            else:
                title = self.create_title_region_content("标题")  # ≤8字
                self.play(Write(title))
                self.region_elements['title'] = title
            
            # 步骤区域
            if self.region_elements["step"] is not None:
                new_step = self.create_step_region_content("第一步")  # ≤12字
                self.play(ReplacementTransform(self.region_elements['step'], new_step))
                self.region_elements['step'] = new_step
            else:
                step = self.create_step_region_content("第一步")  # ≤12字
                self.play(Write(step))
                self.region_elements['step'] = step

            # 主内容区域
            if self.region_elements["main"] is not None:
                new_main = self.create_main_region_content(my_content)
                self.play(ReplacementTransform(self.region_elements['main'], new_main))
                self.region_elements['main'] = new_main
            else:
                main = self.create_main_region_content(my_content)
                self.play(FadeIn(main))
                self.region_elements['main'] = main
    ```

    
    核心特性：
    • 区域自动定位：各区域内容自动适配布局
    • VGroup动画：支持Transform、ReplacementTransform等manim动画
    • 状态管理：可跟踪和更新各区域的当前状态
    • 动画自由度：主内容区域动画完全由用户控制
    • 同步更新：多个区域可同时或分步更新内容