# polygram

Mobjects that are simple geometric shapes.

### Classes

| [`ConvexHull`](manim.mobject.geometry.polygram.ConvexHull.md#manim.mobject.geometry.polygram.ConvexHull)                   | Constructs a convex hull for a set of points in no particular order.                                                                                    |
|----------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`Cutout`](manim.mobject.geometry.polygram.Cutout.md#manim.mobject.geometry.polygram.Cutout)                               | A shape with smaller cutouts.                                                                                                                           |
| [`Polygon`](manim.mobject.geometry.polygram.Polygon.md#manim.mobject.geometry.polygram.Polygon)                            | A shape consisting of one closed loop of vertices.                                                                                                      |
| [`Polygram`](manim.mobject.geometry.polygram.Polygram.md#manim.mobject.geometry.polygram.Polygram)                         | A generalized [`Polygon`](manim.mobject.geometry.polygram.Polygon.md#manim.mobject.geometry.polygram.Polygon), allowing for disconnected sets of edges. |
| [`Rectangle`](manim.mobject.geometry.polygram.Rectangle.md#manim.mobject.geometry.polygram.Rectangle)                      | A quadrilateral with two sets of parallel sides.                                                                                                        |
| [`RegularPolygon`](manim.mobject.geometry.polygram.RegularPolygon.md#manim.mobject.geometry.polygram.RegularPolygon)       | An n-sided regular [`Polygon`](manim.mobject.geometry.polygram.Polygon.md#manim.mobject.geometry.polygram.Polygon).                                     |
| [`RegularPolygram`](manim.mobject.geometry.polygram.RegularPolygram.md#manim.mobject.geometry.polygram.RegularPolygram)    | A [`Polygram`](manim.mobject.geometry.polygram.Polygram.md#manim.mobject.geometry.polygram.Polygram) with regularly spaced vertices.                    |
| [`RoundedRectangle`](manim.mobject.geometry.polygram.RoundedRectangle.md#manim.mobject.geometry.polygram.RoundedRectangle) | A rectangle with rounded corners.                                                                                                                       |
| [`Square`](manim.mobject.geometry.polygram.Square.md#manim.mobject.geometry.polygram.Square)                               | A rectangle with equal side lengths.                                                                                                                    |
| [`Star`](manim.mobject.geometry.polygram.Star.md#manim.mobject.geometry.polygram.Star)                                     | A regular polygram without the intersecting lines.                                                                                                      |
| [`Triangle`](manim.mobject.geometry.polygram.Triangle.md#manim.mobject.geometry.polygram.Triangle)                         | An equilateral triangle.                                                                                                                                |
