from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate

class RNNMemoryMasterScene(ProfessionalScienceTemplate):
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 执行7个阶段的动画
        self.create_stage1_word_input()
        self.create_stage2_initial_memory()
        self.create_stage3_first_memory_update()
        self.create_stage4_first_prediction()
        self.create_stage5_second_memory_update()
        self.create_stage6_second_prediction()
        self.create_stage7_training()

    def create_stage1_word_input(self):
        """阶段1：词语输入与编码"""
        # 标题区域
        title_group = self.create_title_region_content("记忆大师小明")
        if self.region_elements.get("title") is not None:
            self.play(ReplacementTransform(self.region_elements['title'], title_group))
        else:
            self.play(Write(title_group))
        self.region_elements['title'] = title_group

        # 步骤区域
        step_group = self.create_step_region_content("词语输入")
        if self.region_elements.get("step") is not None:
            self.play(ReplacementTransform(self.region_elements['step'], step_group))
        else:
            self.play(Write(step_group))
        self.region_elements['step'] = step_group

        # 主内容区域：卡通小明和输入框
        xiaoming = Circle(radius=0.5, color=BLUE).shift(LEFT*2)
        xiaoming_face = VGroup(
            Dot(xiaoming.get_center() + UP*0.2 + LEFT*0.15, radius=0.05),
            Dot(xiaoming.get_center() + UP*0.2 + RIGHT*0.15, radius=0.05),
            Arc(angle=PI, radius=0.2, color=WHITE).shift(xiaoming.get_center() + DOWN*0.1)
        )
        
        input_box = Rectangle(width=1.5, height=0.8, color=WHITE).shift(RIGHT*1)
        input_text = Text("你", font_size=24).move_to(input_box.get_center())
        vector_arrow = Arrow(start=input_box.get_right(), end=input_box.get_right() + RIGHT*1.5, color=GREEN)
        vector_label = Text("[1,0,0]", font_size=20).next_to(vector_arrow.get_end(), RIGHT)
        
        word_input_group = VGroup(xiaoming, xiaoming_face, input_box, input_text, vector_arrow, vector_label)
        main_content = self.create_main_region_content(word_input_group)
        
        if self.region_elements.get("main") is not None:
            self.play(ReplacementTransform(self.region_elements['main'], main_content))
        else:
            self.play(FadeIn(main_content))
        self.region_elements['main'] = main_content

        # 左辅助区域：词典表格
        dict_table = Table([
            ["词语", "编码"],
            ["你", "[1,0,0]"],
            ["好", "[0,1,0]"],
            ["吗", "[0,0,1]"]
        ], include_outer_lines=True).scale(0.6)
        left_aux = self.create_left_auxiliary_content("词语词典", dict_table)
        
        if self.region_elements.get("left_aux") is not None:
            self.play(ReplacementTransform(self.region_elements['left_aux'], left_aux))
        else:
            self.play(FadeIn(left_aux))
        self.region_elements['left_aux'] = left_aux

        # 右辅助区域：编码信息
        right_aux = self.create_right_auxiliary_content("编码信息", ["你:[1,0,0]"])
        if self.region_elements.get("right_aux") is not None:
            self.play(ReplacementTransform(self.region_elements['right_aux'], right_aux))
        else:
            self.play(FadeIn(right_aux))
        self.region_elements['right_aux'] = right_aux

        # 结果区域
        result_group = self.create_result_region_content("词语'你'被表示为Vector([1,0,0])")
        if self.region_elements.get("result") is not None:
            self.play(ReplacementTransform(self.region_elements['result'], result_group))
        else:
            self.play(Write(result_group))
        self.region_elements['result'] = result_group
        
        self.wait(2)

    def create_stage2_initial_memory(self):
        """阶段2：初始记忆状态"""
        # 步骤更新
        new_step = self.create_step_region_content("初始记忆")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：记忆容器
        memory_container = RoundedRectangle(width=3, height=2, corner_radius=0.3, color=PURPLE)
        memory_label = Text("记忆状态", font_size=20).move_to(memory_container.get_top() + DOWN*0.3)
        initial_vector = Text("[0.1, 0.2]", font_size=24, color=YELLOW).move_to(memory_container.get_center())
        
        memory_group = VGroup(memory_container, memory_label, initial_vector)
        new_main = self.create_main_region_content(memory_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 清空辅助区域
        fade_outs = []
        if self.region_elements.get('left_aux') is not None:
            fade_outs.append(FadeOut(self.region_elements['left_aux']))
        if self.region_elements.get('right_aux') is not None:
            fade_outs.append(FadeOut(self.region_elements['right_aux']))
        if fade_outs:
            self.play(*fade_outs)
        self.region_elements['left_aux'] = None
        self.region_elements['right_aux'] = None

        # 结果更新
        new_result = self.create_result_region_content("获得一个初始记忆状态Vector([0.1, 0.2])")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)

    def create_stage3_first_memory_update(self):
        """阶段3：第一次词语处理与记忆更新"""
        # 步骤更新
        new_step = self.create_step_region_content("记忆更新")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：记忆更新公式
        formula = MathTex(r"h_t = \tanh(W_{xh} \cdot x_t + W_{hh} \cdot h_{t-1} + b_h)")
        input_part = MathTex(r"W_{xh} \cdot [1,0,0] = [0.3, 0.4]", color=GREEN).scale(0.7)
        memory_part = MathTex(r"W_{hh} \cdot [0.1, 0.2] = [0.2, 0.1]", color=BLUE).scale(0.7)
        bias_part = MathTex(r"b_h = [0.1, 0.05]", color=RED).scale(0.7)
        final_calc = MathTex(r"h_1 = \tanh([0.6, 0.55]) = [0.59, 0.55]", color=YELLOW).scale(0.8)
        
        formula_group = VGroup(formula, input_part, memory_part, bias_part, final_calc).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(formula_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 左辅助：输入权重矩阵
        wxh_table = Table([
            ["W_xh", ""],
            ["[0.3", "0.2]"],
            ["[0.4", "0.1]"]
        ], include_outer_lines=True).scale(0.5)
        left_aux = self.create_left_auxiliary_content("输入权重", wxh_table)
        self.play(FadeIn(left_aux))
        self.region_elements['left_aux'] = left_aux

        # 右辅助：记忆权重矩阵
        whh_table = Table([
            ["W_hh", ""],
            ["[0.5", "0.3]"],
            ["[0.2", "0.4]"]
        ], include_outer_lines=True).scale(0.5)
        right_aux = self.create_right_auxiliary_content("记忆权重", whh_table)
        self.play(FadeIn(right_aux))
        self.region_elements['right_aux'] = right_aux

        # 分步显示计算过程
        self.play(Write(formula))
        self.wait(1)
        self.play(Write(input_part))
        self.wait(1)
        self.play(Write(memory_part))
        self.wait(1)
        self.play(Write(bias_part))
        self.wait(1)
        self.play(Write(final_calc))

        # 结果更新
        new_result = self.create_result_region_content("小明新记忆状态更新为Vector([0.59, 0.55])")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)

    def create_stage4_first_prediction(self):
        """阶段4：第一次预测与输出"""
        # 步骤更新
        new_step = self.create_step_region_content("预测输出")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：预测公式和概率分布
        pred_formula = MathTex(r"y_t = W_{hy} \cdot h_t + b_y")
        calc_step = MathTex(r"y_1 = [0.8, 1.2, 1.5]", color=GREEN).scale(0.8)
        softmax_step = MathTex(r"\text{softmax}(y_1) = [0.18, 0.46, 0.36]", color=BLUE).scale(0.8)
        
        # 概率分布条形图
        axes = Axes(x_range=[0, 3, 1], y_range=[0, 0.5, 0.1], 
                   x_length=4, y_length=2.5,
                   axis_config={"include_numbers": False}).scale(0.7)
        # 使用Rectangle创建简单条形图
        bar1 = Rectangle(width=0.6, height=0.18*4, color=RED, fill_opacity=0.8).shift(LEFT*1.5)
        bar2 = Rectangle(width=0.6, height=0.46*4, color=GREEN, fill_opacity=0.8)
        bar3 = Rectangle(width=0.6, height=0.36*4, color=BLUE, fill_opacity=0.8).shift(RIGHT*1.5)
        bars = VGroup(bar1, bar2, bar3)
        bar_labels = VGroup(
            Text("你", font_size=16).next_to(bar1, DOWN),
            Text("好", font_size=16).next_to(bar2, DOWN),
            Text("吗", font_size=16).next_to(bar3, DOWN)
        )
        
        prediction_group = VGroup(pred_formula, calc_step, softmax_step, axes, bars, bar_labels).arrange(DOWN, buff=0.2)
        new_main = self.create_main_region_content(prediction_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 更新辅助区域
        why_table = Table([
            ["W_hy", "", ""],
            ["[0.2", "0.3", "0.5]"],
            ["[0.4", "0.6", "0.2]"]
        ], include_outer_lines=True).scale(0.5)
        new_left_aux = self.create_left_auxiliary_content("输出权重", why_table)
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux

        bias_y = Text("b_y = [0.1, 0.2]", font_size=16)
        new_right_aux = self.create_right_auxiliary_content("偏置项", bias_y)
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux

        # 分步显示预测过程
        self.play(Write(pred_formula))
        self.wait(1)
        self.play(Write(calc_step))
        self.wait(1)
        self.play(Write(softmax_step))
        self.wait(1)
        self.play(Create(axes), Create(bars), Write(bar_labels))

        # 结果更新
        new_result = self.create_result_region_content("预测下一个词是'吗'的概率最高(0.36)")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)

    def create_stage5_second_memory_update(self):
        """阶段5：第二次词语处理与记忆更新（循环）"""
        # 步骤更新
        new_step = self.create_step_region_content("再次记忆更新")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：第二次记忆更新
        formula = MathTex(r"h_2 = \tanh(W_{xh} \cdot x_2 + W_{hh} \cdot h_1 + b_h)")
        input_part2 = MathTex(r"W_{xh} \cdot [0,1,0] = [0.2, 0.1]", color=GREEN).scale(0.7)
        memory_part2 = MathTex(r"W_{hh} \cdot [0.59, 0.55] = [0.46, 0.34]", color=BLUE).scale(0.7)
        bias_part2 = MathTex(r"b_h = [0.1, 0.05]", color=RED).scale(0.7)
        final_calc2 = MathTex(r"h_2 = \tanh([0.76, 0.49]) = [0.71, 0.78]", color=YELLOW).scale(0.8)
        
        formula_group2 = VGroup(formula, input_part2, memory_part2, bias_part2, final_calc2).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(formula_group2)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 恢复权重矩阵辅助区域
        wxh_table = Table([
            ["W_xh", ""],
            ["[0.3", "0.2]"],
            ["[0.4", "0.1]"]
        ], include_outer_lines=True).scale(0.5)
        new_left_aux = self.create_left_auxiliary_content("输入权重", wxh_table)
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux

        whh_table = Table([
            ["W_hh", ""],
            ["[0.5", "0.3]"],
            ["[0.2", "0.4]"]
        ], include_outer_lines=True).scale(0.5)
        new_right_aux = self.create_right_auxiliary_content("记忆权重", whh_table)
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux

        # 分步显示第二次计算
        self.play(Write(formula))
        self.wait(1)
        self.play(Write(input_part2))
        self.wait(1)
        self.play(Write(memory_part2))
        self.wait(1)
        self.play(Write(bias_part2))
        self.wait(1)
        self.play(Write(final_calc2))

        # 结果更新
        new_result = self.create_result_region_content("小明新记忆状态更新为Vector([0.71, 0.78])")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)

    def create_stage6_second_prediction(self):
        """阶段6：第二次预测与输出"""
        # 步骤更新
        new_step = self.create_step_region_content("再次预测")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：第二次预测
        pred_formula2 = MathTex(r"y_2 = W_{hy} \cdot h_2 + b_y")
        calc_step2 = MathTex(r"y_2 = [0.9, 1.4, 1.8]", color=GREEN).scale(0.8)
        softmax_step2 = MathTex(r"\text{softmax}(y_2) = [0.15, 0.44, 0.41]", color=BLUE).scale(0.8)
        
        # 更新的概率分布
        axes2 = Axes(x_range=[0, 3, 1], y_range=[0, 0.5, 0.1], 
                    x_length=4, y_length=2.5,
                    axis_config={"include_numbers": False}).scale(0.7)
        # 使用Rectangle创建第二个条形图
        bar2_1 = Rectangle(width=0.6, height=0.15*4, color=RED, fill_opacity=0.8).shift(LEFT*1.5)
        bar2_2 = Rectangle(width=0.6, height=0.44*4, color=GREEN, fill_opacity=0.8)
        bar2_3 = Rectangle(width=0.6, height=0.41*4, color=BLUE, fill_opacity=0.8).shift(RIGHT*1.5)
        bars2 = VGroup(bar2_1, bar2_2, bar2_3)
        bar_labels2 = VGroup(
            Text("你", font_size=16).next_to(bar2_1, DOWN),
            Text("好", font_size=16).next_to(bar2_2, DOWN),
            Text("吗", font_size=16).next_to(bar2_3, DOWN)
        )
        
        prediction_group2 = VGroup(pred_formula2, calc_step2, softmax_step2, axes2, bars2, bar_labels2).arrange(DOWN, buff=0.2)
        new_main = self.create_main_region_content(prediction_group2)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 更新辅助区域
        why_table2 = Table([
            ["W_hy", "", ""],
            ["[0.2", "0.3", "0.5]"],
            ["[0.4", "0.6", "0.2]"]
        ], include_outer_lines=True).scale(0.5)
        new_left_aux = self.create_left_auxiliary_content("输出权重", why_table2)
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux

        bias_y2 = Text("b_y = [0.1, 0.2]", font_size=16)
        new_right_aux = self.create_right_auxiliary_content("偏置项", bias_y2)
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux

        # 分步显示第二次预测
        self.play(Write(pred_formula2))
        self.wait(1)
        self.play(Write(calc_step2))
        self.wait(1)
        self.play(Write(softmax_step2))
        self.wait(1)
        self.play(Create(axes2), Create(bars2), Write(bar_labels2))

        # 高亮显示'吗'的概率提高
        self.play(bar2_3.animate.set_color(YELLOW), Flash(bar2_3))

        # 结果更新
        new_result = self.create_result_region_content("预测下一个词是'吗'的概率更高了(0.41)")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(2)

    def create_stage7_training(self):
        """阶段7：训练与学习（实际应用）"""
        # 步骤更新
        new_step = self.create_step_region_content("训练学习")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step

        # 主内容：训练循环图
        # 创建训练循环的流程图
        forward_box = Rectangle(width=2, height=0.8, color=GREEN).shift(UP*2)
        forward_text = Text("前向传播", font_size=16).move_to(forward_box.get_center())
        
        loss_box = Rectangle(width=2, height=0.8, color=RED).shift(RIGHT*3)
        loss_text = Text("计算损失", font_size=16).move_to(loss_box.get_center())
        
        backward_box = Rectangle(width=2, height=0.8, color=BLUE).shift(DOWN*2)
        backward_text = Text("反向传播", font_size=16).move_to(backward_box.get_center())
        
        update_box = Rectangle(width=2, height=0.8, color=PURPLE).shift(LEFT*3)
        update_text = Text("更新权重", font_size=16).move_to(update_box.get_center())
        
        # 连接箭头
        arrow1 = Arrow(forward_box.get_right(), loss_box.get_left(), color=WHITE)
        arrow2 = Arrow(loss_box.get_bottom(), backward_box.get_right(), color=WHITE)
        arrow3 = Arrow(backward_box.get_left(), update_box.get_right(), color=WHITE)
        arrow4 = Arrow(update_box.get_top(), forward_box.get_left(), color=WHITE)
        
        training_loop = VGroup(
            forward_box, forward_text, loss_box, loss_text,
            backward_box, backward_text, update_box, update_text,
            arrow1, arrow2, arrow3, arrow4
        ).scale(0.6)
        
        new_main = self.create_main_region_content(training_loop)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main

        # 清空左辅助区域
        if self.region_elements.get('left_aux') is not None:
            self.play(FadeOut(self.region_elements['left_aux']))
        self.region_elements['left_aux'] = None

        # 右辅助：损失函数
        loss_formula = MathTex(r"L = -\sum_{i} y_i \log(\hat{y}_i)", font_size=20)
        new_right_aux = self.create_right_auxiliary_content("损失函数", loss_formula)
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux

        # 动画显示训练循环
        self.play(FadeIn(forward_box), Write(forward_text))
        self.wait(0.5)
        self.play(Create(arrow1))
        self.play(FadeIn(loss_box), Write(loss_text))
        self.play(Flash(loss_box))
        self.wait(0.5)
        self.play(Create(arrow2))
        self.play(FadeIn(backward_box), Write(backward_text))
        self.play(Flash(backward_box))
        self.wait(0.5)
        self.play(Create(arrow3))
        self.play(FadeIn(update_box), Write(update_text))
        self.wait(0.5)
        self.play(Create(arrow4))

        # 结果更新
        new_result = self.create_result_region_content("小明学会了更准确地预测序列。")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(3)

if __name__ == "__main__":
    # 渲染命令: manim -pql rnn_memory_master_scene.py RNNMemoryMasterScene
    pass 