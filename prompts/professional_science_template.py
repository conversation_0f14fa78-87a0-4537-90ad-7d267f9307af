from manim import *
import numpy as np
from typing import Dict, Any, List, Optional, Union
from collections import defaultdict


class ProfessionalScienceTemplate(Scene):
    """
    世界级专业理科原理讲解模板（增强版）
    
    新增功能：
    - 连贯性动画支持：内容在原有基础上变化，而不是完全替换
    - 状态管理系统：跨步骤信息存储和状态追踪
    - 增量更新机制：支持渐进式内容变化
    - 动画序列管理：复杂多步动画的协调
    
    布局结构：
    ┌─────────────────┬─────────────────┐
    │  标题区域       │   步骤介绍区域   │  <- 各占顶部10%
    │  (TOP_LEFT)     │   (TOP_RIGHT)   │
    ├────┬─────────────────────────┬────┤
    │辅助│                          |辅助│
    │区域│      主内容区域           │区域│  <- 左15%，中60%，右15%
    │LEFT│      (CENTER)           │RIGH│
    │    │                         │T   │
    │    │                         │    │
    ├────┴─────────────────────────┴────┤
    │           结果区域                │  <- 底部10%
    │           (DOWN)                  │
    └───────────────────────────────────┘
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # 区域元素记录字典
        self.region_elements = {
            'title': None,
            'step': None,
            'main': None,
            'left_auxiliary': None,
            'right_auxiliary': None,
            'result': None
        }
        
        # 专业色彩系统 - 黑色背景下的亮色配色
        self.colors = {
            'primary': "#FDE047",      # 主要黄色 - 突出内容使用
            'secondary': "#FACC15",    # 次要金黄色 - 强调色，突出内容
            'accent': "#F59E0B",       # 强调橙黄色 - 重点标记，突出内容
            'success': "#EAB308",      # 成功金色 - 结果展示，突出内容
            'background': "#F5F5F5",   # 背景灰色 - 舒适背景
            'text_primary': WHITE,     # 主要文字颜色 - 纯白色，标题和普通文案
            'text_secondary': WHITE,   # 次要文字颜色 - 纯白色，标题和普通文案
            'grid': "#DDD",            # 网格线颜色
            'highlight': "#FBBF24",    # 高亮黄色 - 特殊标记
            'auxiliary_text': WHITE,   # 辅助区域文字颜色 - 纯白色更亮
            'auxiliary_bg': "#2D3436", # 辅助区域背景颜色
            'continuity': "#10B981",   # 连贯性标记颜色 - 绿色
            'transition': "#8B5CF6",   # 过渡动画颜色 - 紫色
            'persistent': "#EF4444"    # 持久化元素颜色 - 红色
        }
        
        # 字体大小系统 - 层次分明的视觉层级
        self.font_sizes = {
            'title': 36,        # 标题字体 - 增大
            'step': 32,         # 步骤介绍字体 - 增大
            'main_content': 36, # 主内容字体
            'auxiliary': 22,    # 辅助信息字体 - 增大
            'auxiliary_detail': 18,  # 辅助详细信息字体 - 增大
            'result': 24,       # 结果字体
            'formula': 32,      # 公式字体
            'annotation': 16    # 注释字体
        }
        
        # 区域位置配置 - 精确的布局控制
        self.regions = {
            'title_width': 6,         # 标题区域宽度
            'title_height': 2,        # 标题区域高度
            'step_width': 8,          # 步骤区域宽度
            'step_height': 1.5,         # 步骤区域高度
            'main_width': 8.0,          # 主内容区域宽度（增大）
            'main_height': 4.0,         # 主内容区域高度（增大）
            'auxiliary_width': 3.0,     # 辅助区域宽度（减小）
            'auxiliary_height': 4.0,    # 辅助区域高度
            'result_width': 10.0,        # 结果区域宽度
            'result_height': 1.5        # 结果区域高度
        }
        
        # 是否显示开发备注
        self.show_debug_labels = False
        
        # 自动缩放配置
        self.auto_scale_config = {
            'min_scale_factor': 0.3,    # 最小缩放比例
            'max_scale_factor': 2.0,    # 最大缩放比例
            'scale_step': 0.1,          # 缩放步长
            'padding_ratio': 0.95,      # 内容占区域的比例（留5%边距）
            'small_content_threshold': 0.4,  # 内容太小的阈值（占区域40%以下算太小）
            'enlarge_target_ratio': 0.9,     # 放大目标比例（放大到区域的90%）
            'min_font_size': 18,        # 最小字体大小（确保可读性）
            'max_font_size': 48         # 最大字体大小
        }
        
        # 连贯性动画配置
        self.continuity_config = {
            'transition_duration': 1.0,    # 过渡动画持续时间
            'highlight_duration': 0.5,     # 高亮动画持续时间
            'fade_duration': 0.3,           # 淡入淡出持续时间
            'morph_duration': 1.5,          # 形变动画持续时间
            'pause_between_steps': 0.5,     # 步骤间暂停时间
            'enable_smooth_transitions': True,  # 启用平滑过渡
            'keep_main_elements': True,     # 保持主要元素
            'auto_highlight_changes': True  # 自动高亮变化部分
        }
        
        # 内容建议指南 - 确保最佳视觉效果
        self.content_guidelines = {
            'title': {'max_chars': 8, 'description': '标题建议8个字以内'},
            'step': {'max_chars': 12, 'description': '步骤建议12个字以内'},
            'auxiliary_title': {'max_chars': 6, 'description': '辅助标题建议6个字以内'},
            'auxiliary_items': {'max_items': 5, 'max_chars_per_item': 15, 'description': '辅助项目建议5项以内，每项15字以内'},
            'result': {'max_chars': 40, 'description': '结果描述建议40个字以内'}
        }
    
    def construct(self):
        """场景构建主函数 - 子类应该重写此方法"""
        # 设置背景和网格
        self.setup_background()
        
    def setup_background(self):
        """设置专业背景（无网格线）"""
        # 背景保持干净，无网格线
        pass
    
    # === 自动缩放功能 ===
    
    def auto_fit_content(self, content, max_width, max_height, target_position=ORIGIN):
        """
        自动调整内容大小以适应指定区域（支持放大和缩小）
        
        Args:
            content: 要调整的内容对象
            max_width: 最大宽度
            max_height: 最大高度
            target_position: 目标位置
            
        Returns:
            调整后的内容对象
        """
        if not hasattr(content, 'width') or not hasattr(content, 'height'):
            return content
            
        # 获取内容的实际尺寸
        content_width = content.width
        content_height = content.height
        
        # 如果内容尺寸为0，直接返回
        if content_width == 0 or content_height == 0:
            content.move_to(target_position)
            return content
        
        # 计算可用空间（留出边距）
        available_width = max_width * self.auto_scale_config['padding_ratio']
        available_height = max_height * self.auto_scale_config['padding_ratio']
        
        # 计算当前内容占区域的比例
        width_ratio = content_width / max_width
        height_ratio = content_height / max_height
        current_ratio = max(width_ratio, height_ratio)
        
        # 判断是否需要调整
        scale_factor = 1.0
        
        if current_ratio > self.auto_scale_config['padding_ratio']:
            # 内容太大，需要缩小
            scale_x = available_width / content_width
            scale_y = available_height / content_height
            scale_factor = min(scale_x, scale_y)
            # 应用最小缩放限制
            scale_factor = max(scale_factor, self.auto_scale_config['min_scale_factor'])
            
        elif current_ratio < self.auto_scale_config['small_content_threshold']:
            # 内容太小，考虑放大（但保持留白）
            target_width = max_width * self.auto_scale_config['enlarge_target_ratio']
            target_height = max_height * self.auto_scale_config['enlarge_target_ratio']
            
            scale_x = target_width / content_width
            scale_y = target_height / content_height
            scale_factor = min(scale_x, scale_y)
            
            # 应用最大缩放限制
            scale_factor = min(scale_factor, self.auto_scale_config['max_scale_factor'])
            
            # 确保放大后不超出可用空间
            new_width = content_width * scale_factor
            new_height = content_height * scale_factor
            if new_width > available_width or new_height > available_height:
                scale_x = available_width / content_width
                scale_y = available_height / content_height
                scale_factor = min(scale_x, scale_y)
        
        # 应用缩放
        if abs(scale_factor - 1.0) > 0.01:  # 只有当缩放比例显著不同时才应用
            content.scale(scale_factor)
            
        # 移动到目标位置
        content.move_to(target_position)
        
        return content
    
    def auto_fit_text_content(self, text_content, max_width, max_height, target_position=ORIGIN):
        """
        自动调整文本内容大小以适应指定区域（支持放大和缩小）
        对于文本，优先调整字体大小而不是整体缩放
        
        Args:
            text_content: 文本内容对象
            max_width: 最大宽度
            max_height: 最大高度
            target_position: 目标位置
            
        Returns:
            调整后的文本内容对象
        """
        if not hasattr(text_content, 'width') or not hasattr(text_content, 'height'):
            return text_content
            
        # 如果内容尺寸为0，直接返回
        if text_content.width == 0 or text_content.height == 0:
            text_content.move_to(target_position)
            return text_content
            
        # 获取可用空间
        available_width = max_width * self.auto_scale_config['padding_ratio']
        available_height = max_height * self.auto_scale_config['padding_ratio']
        
        # 计算当前内容占区域的比例
        width_ratio = text_content.width / max_width
        height_ratio = text_content.height / max_height
        current_ratio = max(width_ratio, height_ratio)
        
        # 获取初始字体大小
        initial_font_size = self.get_text_font_size(text_content)
        
        if current_ratio > self.auto_scale_config['padding_ratio']:
            # 内容太大，需要缩小
            # 优先减小字体大小
            iteration = 0
            max_iterations = 15  # 防止无限循环
            
            while (text_content.width > available_width or text_content.height > available_height) and iteration < max_iterations:
                current_font_size = self.get_text_font_size(text_content)
                new_font_size = max(current_font_size * 0.92, self.auto_scale_config['min_font_size'])
                
                self.set_text_font_size(text_content, new_font_size)
                iteration += 1
                
                # 如果字体已经达到最小值，退出循环
                if new_font_size <= self.auto_scale_config['min_font_size']:
                    break
            
            # 如果仍然超出范围，使用整体缩放作为后备方案
            if text_content.width > available_width or text_content.height > available_height:
                scale_x = available_width / text_content.width
                scale_y = available_height / text_content.height
                scale_factor = min(scale_x, scale_y)
                scale_factor = max(scale_factor, self.auto_scale_config['min_scale_factor'])
                
                if scale_factor < 1.0:
                    text_content.scale(scale_factor)
                    
        elif current_ratio < self.auto_scale_config['small_content_threshold']:
            # 内容太小，考虑放大字体（但保持留白）
            target_width = max_width * self.auto_scale_config['enlarge_target_ratio']
            target_height = max_height * self.auto_scale_config['enlarge_target_ratio']
            
            # 估算需要的字体放大倍数
            scale_x = target_width / text_content.width
            scale_y = target_height / text_content.height
            scale_factor = min(scale_x, scale_y)
            
            # 限制最大放大倍数
            scale_factor = min(scale_factor, self.auto_scale_config['max_scale_factor'])
            
            if scale_factor > 1.1:  # 只有当需要明显放大时才调整
                # 尝试增大字体
                new_font_size = min(initial_font_size * scale_factor, self.auto_scale_config['max_font_size'])
                self.set_text_font_size(text_content, new_font_size)
                
                # 检查放大后是否超出边界
                if text_content.width > available_width or text_content.height > available_height:
                    # 如果超出，回退并使用更保守的放大
                    self.set_text_font_size(text_content, initial_font_size)
                    
                    # 使用整体缩放进行适度放大
                    conservative_scale = min(
                        available_width / text_content.width,
                        available_height / text_content.height,
                        1.5  # 最大1.5倍放大
                    )
                    
                    if conservative_scale > 1.05:
                        text_content.scale(conservative_scale)
        
        # 移动到目标位置
        text_content.move_to(target_position)
        
        return text_content
    
    def get_text_font_size(self, text_content):
        """获取文本内容的字体大小"""
        if hasattr(text_content, 'font_size'):
            return text_content.font_size
        elif isinstance(text_content, VGroup) and len(text_content.submobjects) > 0:
            # 对于VGroup，获取第一个有font_size属性的子对象的字体大小
            for submob in text_content.submobjects:
                if hasattr(submob, 'font_size'):
                    return submob.font_size
        return 12  # 默认字体大小
    
    def set_text_font_size(self, text_content, font_size):
        """设置文本内容的字体大小"""
        if hasattr(text_content, 'font_size'):
            text_content.font_size = font_size
            
        # 如果是VGroup，递归处理所有子对象
        if isinstance(text_content, VGroup):
            for submob in text_content.submobjects:
                if hasattr(submob, 'font_size'):
                    submob.font_size = font_size
                elif isinstance(submob, VGroup):
                    self.set_text_font_size(submob, font_size)
    
    # === 标准化区域内容创建接口 ===
    
    def create_title_region_content(self, title_text):
        """
        创建标题区域内容的标准接口（固定字体大小）
        
        功能说明：
        - 自动定位到左上角标题区域（3.5×0.8）
        - 使用固定字体大小，保持视觉一致性
        - 使用专业蓝色主题色
        
        内容建议：
        - 标题长度：建议8个字以内（如"数学原理"、"物理定律"）
        - 避免过长：如"机器学习中的深度神经网络算法详解"
        
        Args:
            title_text (str): 标题文字
        
        Returns:
            VGroup: 定位到标题区域的内容组，包含固定字体大小的文字
            
        使用示例：
            title_group = self.create_title_region_content("函数原理")
            self.play(Write(title_group))
            
            # 所有标题都使用相同字体大小，保持一致性
            title = self.create_title_region_content("复杂算法演示")
        """
        title_content = Text(
            title_text,
            font_size=self.font_sizes['title'],
            color=WHITE,  # 标题用白色
            font="Arial Black",
            weight=BOLD
        )
        
        # 计算标题区域位置并直接定位，不进行自动缩放
        title_position = UP * 3.5 + LEFT * 5
        title_content.move_to(title_position)
        
        title_group = VGroup(title_content)
        return title_group
    
    def create_step_region_content(self, step_text):
        """
        创建步骤区域内容的标准接口（固定字体大小）
        
        功能说明：
        - 自动定位到右上角步骤区域（3.5×0.8）
        - 使用固定字体大小，保持视觉一致性
        - 使用紫色主题色，与标题区域形成对比
        
        内容建议：
        - 步骤长度：建议12个字以内（如"第一步：数据预处理"）
        - 常用格式：
          * "第一步：初始化"
          * "阶段1：数据收集"
          * "步骤A：建立模型"
        
        Args:
            step_text (str): 步骤文字
        
        Returns:
            VGroup: 定位到步骤区域的内容组，包含固定字体大小的文字
            
        使用示例：
            step_group = self.create_step_region_content("第一步：定义函数")
            self.play(Write(step_group))
            
            # 动态更新步骤，所有步骤使用相同字体大小
            step2 = self.create_step_region_content("第二步：求导分析")
            self.play(Transform(step_group, step2))
        """
        step_content = Text(
            step_text,
            font_size=self.font_sizes['step'],
            color=WHITE,  # 步骤描述用白色
            font="Arial"
        )
        
        # 计算步骤区域位置并直接定位，不进行自动缩放
        step_position = UP * 3.5 + RIGHT * 3.5
        step_content.move_to(step_position)
        
        step_group = VGroup(step_content)
        return step_group
    
    def create_main_region_content(self, main_content):
        """
        创建主内容区域内容的标准接口（自动适配区域大小）
        
        功能说明：
        - 自动定位到屏幕中央主内容区域（6.0×3.5）
        - 内容自动缩放适应区域，保持15%边距
        - 支持任何Mobject对象：图形、动画、文字等
        
        区域优势：
        - 大空间：比原版增大33%，适合复杂可视化
        - 灵活性：可容纳多个图形、坐标系、动画序列
        - 居中显示：视觉焦点，最佳观看效果
        
        Args:
            main_content: 主要内容对象（VGroup、图形、动画等）
        
        Returns:
            VGroup: 定位到主内容区域的内容组，包含自动适配的内容
            
        使用示例：
            # 数学函数图
            axes = Axes(x_range=[-3,3], y_range=[-2,4])
            curve = axes.plot(lambda x: x**2)
            main_group = self.create_main_region_content(VGroup(axes, curve))
            
            # 算法动画
            sorting_animation = self.create_sorting_visualization()
            main_group = self.create_main_region_content(sorting_animation)
            
            # 复杂图形组合
            complex_diagram = VGroup(circles, arrows, labels)
            main_group = self.create_main_region_content(complex_diagram)
        """
        # 主内容区域位置（中央）
        main_position = ORIGIN
        
        # 自动适配内容大小
        main_content = self.auto_fit_content(
            main_content,
            max_width=self.regions['main_width'],
            max_height=self.regions['main_height'],
            target_position=main_position
        )
        
        main_group = VGroup(main_content)
        return main_group
    
    def create_left_auxiliary_content(self, title, items, use_background=True):
        """
        创建左侧辅助区域内容的标准接口（自动适配区域大小）
        
        功能说明：
        - 自动定位到左侧辅助区域（1.4×2.5）
        - 纯白色文字，深色背景，高对比度
        - 字体自动缩放，最小18px，确保可读性
        - 紧凑布局，突出关键信息
        
        内容建议：
        - 标题：6个字以内（如"要点"、"概念"、"步骤"）
        - 项目数：建议5项以内
        - 每项长度：15个字以内
        - 适用场景：
          * 关键概念列表
          * 重要步骤说明
          * 核心要点提醒
        
        Args:
            title (str): 辅助区域标题
            items (list): 辅助信息列表（支持字符串和MathTex）
            use_background (bool): 是否使用深色背景（默认True）
        
        Returns:
            VGroup: 定位到左侧辅助区域的内容组，包含自动适配的内容
            
        使用示例：
            # 概念要点
            left_aux = self.create_left_auxiliary_content("要点:", [
                "• 开口向上",
                "• 顶点(0,0)",
                "• 对称轴x=0"
            ])
            
            # 算法步骤
            steps = self.create_left_auxiliary_content("步骤:", [
                "1. 初始化数据",
                "2. 开始循环",
                "3. 比较交换",
                "4. 检查完成"
            ])
            
            # 混合内容（文字+公式）
            mixed = self.create_left_auxiliary_content("公式:", [
                "导数公式:",
                MathTex(r"f'(x) = 2x"),
                "积分公式:",
                MathTex(r"\int x dx")
            ])
        """
        # 创建标题
        aux_title = Text(
            title, 
            font_size=self.font_sizes['auxiliary'], 
            color=self.colors['auxiliary_text'], 
            weight=BOLD
        )
        
        # 创建项目列表
        aux_items = []
        for item in items:
            if isinstance(item, str):
                aux_item = Text(
                    item, 
                    font_size=self.font_sizes['auxiliary_detail'], 
                    color=self.colors['auxiliary_text']
                )
            else:
                aux_item = item  # 如果是MathTex或其他对象，直接使用
                aux_item.set_color(self.colors['auxiliary_text'])
            aux_items.append(aux_item)
        
        # 组合所有元素
        all_items = [aux_title] + aux_items
        content = VGroup(*all_items).arrange(DOWN, aligned_edge=LEFT, buff=0.25)
        
        # 如果需要背景
        if use_background:
            bg_rect = Rectangle(
                width=content.width + 0.4,
                height=content.height + 0.3,
                fill_color=self.colors['auxiliary_bg'],
                fill_opacity=0.8,
                stroke_width=0
            ).move_to(content.get_center())
            content_group = VGroup(bg_rect, content)
        else:
            content_group = VGroup(content)
        
        # 左侧辅助区域位置（调整位置以适应主内容区域增大）
        left_position = LEFT * 5.0
        
        # 自动适配内容大小
        content_group = self.auto_fit_text_content(
            content_group,
            max_width=self.regions['auxiliary_width'],
            max_height=self.regions['auxiliary_height'],
            target_position=left_position
        )
        
        return content_group
    
    def create_right_auxiliary_content(self, title, items, use_background=True):
        """
        创建右侧辅助区域内容的标准接口（自动适配区域大小）
        
        功能说明：
        - 自动定位到右侧辅助区域（1.4×2.5）
        - 纯白色文字，深色背景，高对比度
        - 字体自动缩放，最小18px，确保可读性
        - 与左侧区域对称，提供补充信息
        
        内容建议：
        - 标题：6个字以内（如"公式"、"特点"、"参数"）
        - 项目数：建议5项以内
        - 每项长度：15个字以内
        - 适用场景：
          * 相关公式展示
          * 算法特点说明
          * 参数设置列表
          * 补充信息注释
        
        Args:
            title (str): 辅助区域标题
            items (list): 辅助信息列表（支持字符串和MathTex）
            use_background (bool): 是否使用深色背景（默认True）
        
        Returns:
            VGroup: 定位到右侧辅助区域的内容组，包含自动适配的内容
            
        使用示例：
            # 数学公式
            formulas = self.create_right_auxiliary_content("公式:", [
                MathTex(r"f'(x) = 2x"),
                MathTex(r"f''(x) = 2"),
                "二阶导数为常数"
            ])
            
            # 算法特点
            features = self.create_right_auxiliary_content("特点:", [
                "• 时间复杂度O(n²)",
                "• 空间复杂度O(1)",
                "• 稳定排序算法",
                "• 适合小数据集"
            ])
            
            # 参数设置
            params = self.create_right_auxiliary_content("参数:", [
                "• 学习率: 0.01",
                "• 批次大小: 32",
                "• 训练轮数: 100"
            ])
        """
        # 创建标题
        aux_title = Text(
            title, 
            font_size=self.font_sizes['auxiliary'], 
            color=self.colors['auxiliary_text'], 
            weight=BOLD
        )
        
        # 创建项目列表
        aux_items = []
        for item in items:
            if isinstance(item, str):
                aux_item = Text(
                    item, 
                    font_size=self.font_sizes['auxiliary_detail'], 
                    color=self.colors['auxiliary_text']
                )
            else:
                aux_item = item  # 如果是MathTex或其他对象，直接使用
                aux_item.set_color(self.colors['auxiliary_text'])
            aux_items.append(aux_item)
        
        # 组合所有元素
        all_items = [aux_title] + aux_items
        content = VGroup(*all_items).arrange(DOWN, aligned_edge=LEFT, buff=0.25)
        
        # 如果需要背景
        if use_background:
            bg_rect = Rectangle(
                width=content.width + 0.4,
                height=content.height + 0.3,
                fill_color=self.colors['auxiliary_bg'],
                fill_opacity=0.8,
                stroke_width=0
            ).move_to(content.get_center())
            content_group = VGroup(bg_rect, content)
        else:
            content_group = VGroup(content)
        
        # 右侧辅助区域位置（调整位置以适应主内容区域增大）
        right_position = RIGHT * 5.0
        
        # 自动适配内容大小
        content_group = self.auto_fit_text_content(
            content_group,
            max_width=self.regions['auxiliary_width'],
            max_height=self.regions['auxiliary_height'],
            target_position=right_position
        )
        
        return content_group
    
    def create_result_region_content(self, result_text):
        """
        创建结果区域内容的标准接口（自动适配区域大小）
        
        功能说明：
        - 自动定位到底部结果区域（7.0×0.8）
        - 字体自动缩放，最小18px，确保可读性
        - 使用成功色（红色），突出重要结论
        - 横跨整个屏幕宽度，重点展示结果
        
        内容建议：
        - 文字长度：建议40个字以内
        - 内容类型：
          * 实验结论总结
          * 算法执行结果
          * 重要发现陈述
          * 学习要点概括
        - 格式建议：
          * "结论：..."
          * "结果：..."
          * "总结：..."
        
        Args:
            result_text (str): 结果描述文字
        
        Returns:
            VGroup: 定位到结果区域的内容组，包含自动适配的文字
            
        使用示例：
            # 数学结论
            result = self.create_result_region_content(
                "结论：二次函数具有开口向上的抛物线形状，在顶点处取得最小值"
            )
            
            # 算法结果
            result = self.create_result_region_content(
                "排序完成：数组[64,34,25,12,22]已按升序排列为[12,22,25,34,64]"
            )
            
            # 实验总结
            result = self.create_result_region_content(
                "实验表明：在理想条件下，抛物运动轨迹完全符合理论预测"
            )
        """
        result_content = Text(
            result_text,
            font_size=self.font_sizes['result'],
            color=self.colors['success']
        )
        
        # 结果区域位置（底部中央）
        result_position = DOWN * 3.0
        
        # 自动适配内容大小
        result_content = self.auto_fit_text_content(
            result_content,
            max_width=self.regions['result_width'],
            max_height=self.regions['result_height'],
            target_position=result_position
        )
        
        result_group = VGroup(result_content)
        return result_group
    
  

# === 使用说明 ===
"""
专业科学模板使用指南：

1. 继承ProfessionalScienceTemplate类
2. 重写construct方法
3. 使用以下标准接口方法创建各区域内容：
   
   - self.create_title_region_content(title): 创建标题区域
   - self.create_step_region_content(step): 创建步骤区域
   - self.create_main_region_content(content): 创建主内容区域
   - self.create_left_auxiliary_content(title, items): 创建左辅助区域
   - self.create_right_auxiliary_content(title, items): 创建右辅助区域
   - self.create_result_region_content(result): 创建结果区域

4. 📝 内容建议指南（确保最佳效果）：
   
   区域类型 | 建议限制 | 示例
   --------|---------|-------
   标题区域 | 8个字以内 | "数学原理"、"物理定律"
   步骤区域 | 12个字以内 | "第一步：数据预处理"
   辅助标题 | 6个字以内 | "要点"、"公式"、"特点"
   辅助项目 | 5项×15字/项 | "• 开口向上"、"• 时间复杂度O(n²)"
   结果区域 | 40个字以内 | "结论：二次函数具有抛物线形状..."

5. 快速开始模板：
    ```python
    from prompts.professional_science_template import ProfessionalScienceTemplate
    
    class MyExample(ProfessionalScienceTemplate):
        def construct(self):
            # 必须调用
            self.setup_background()
            
            # 基本布局
            title = self.create_title_region_content("标题")  # ≤8字
            step = self.create_step_region_content("第一步")  # ≤12字
            main = self.create_main_region_content(my_content)
            result = self.create_result_region_content("结论")  # ≤40字
            
            # 可选：辅助区域
            left = self.create_left_auxiliary_content("要点:", items)
            right = self.create_right_auxiliary_content("公式:", formulas)
            
            # 动画展示
            self.play(Write(title), Write(step))
            self.play(FadeIn(main))
            self.play(FadeIn(left), FadeIn(right))  # 可选
            self.play(Write(result))
    ```

6. 复杂多步动画模板：
    
    对于涉及多个步骤的复杂教学场景，您可以自由控制各区域内容的动态更新：
    
    ```python
    class ComplexAnimation(ProfessionalScienceTemplate):
        def construct(self):
            self.setup_background()
            
            # 创建持久化标题
            title = self.create_title_region_content("算法演示")
            self.add(title)
            
            # 多步骤动画序列
            steps_data = [
                {"step": "第一步：初始化", "main": "self.create_step1_content()", "left": ["• 设置数组", "• 定义变量"], "result": "初始化完成"},
                {"step": "第二步：排序", "main": "self.create_step2_content()", "left": ["• 比较元素", "• 交换位置"], "result": "正在排序..."},
                {"step": "第三步：完成", "main": "self.create_step3_content()", "left": ["• 验证结果", "• 输出数组"], "result": "排序完成！"}
            ]
            
            # 动态执行多步骤
            current_step = current_main = current_left = current_result = None
            
            for i, data in enumerate(steps_data):
                # 创建新内容
                new_step = self.create_step_region_content(data["step"])
                new_main = self.create_main_region_content(data["main"])
                new_left = self.create_left_auxiliary_content("进度:", data["left"])
                new_result = self.create_result_region_content(data["result"])
                
                if i == 0:
                    # 首次显示
                    self.play(Write(new_step), FadeIn(new_main), FadeIn(new_left), Write(new_result))
                else:
                    # 区域间转换动画 - 用户自定义VGroup动画
                    animations = []
                    if current_step: 
                        animations.append(Transform(current_step, new_step))
                    if current_left: 
                        animations.append(Transform(current_left, new_left))
                    if current_result: 
                        animations.append(Transform(current_result, new_result))
                    
                    # 主内容区域特殊动画（用户自由实现）
                    animations.append(self.create_main_transition(current_main, new_main))
                    
                    self.play(*animations)
                    self.wait(1)
                
                # 更新当前引用
                current_step = new_step
                current_main = new_main 
                current_left = new_left
                current_result = new_result
         
         def create_main_transition(self, old_content, new_content):
             # 自定义主内容区域转换动画
             # 用户可自由实现：渐变、滑动、缩放、形变等
             return ReplacementTransform(old_content, new_content)
             
         def create_step1_content(self):
             # 第一步的主内容 - 用户自定义
             return VGroup(Circle(), Text("初始化"))
             
         def create_step2_content(self):
             #第二步的主内容 - 用户自定义
             return VGroup(Square(), Text("处理中"))
             
         def create_step3_content(self):
             #第三步的主内容 - 用户自定义
             return VGroup(Triangle(), Text("完成"))
    ```
    
    核心特性：
    • 区域自动定位：各区域内容自动适配布局
    • VGroup动画：支持Transform、ReplacementTransform等manim动画
    • 状态管理：可跟踪和更新各区域的当前状态
    • 动画自由度：主内容区域动画完全由用户控制
    • 同步更新：多个区域可同时或分步更新内容

""" 