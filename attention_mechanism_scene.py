from manim import *
from prompts.professional_science_template import ProfessionalScienceTemplate
import numpy as np

class AttentionMechanismScene(ProfessionalScienceTemplate):
    """注意力机制专业教学场景"""
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 初始化数据
        self.init_data()
        
        # 执行各个阶段
        self.stage1_introduction()
        self.stage2_calculate_vectors_scores()
        self.stage3_softmax_normalization()
        self.stage4_context_vector()
        self.stage5_generate_translation()
    
    def init_data(self):
        """初始化注意力机制数据"""
        # 输入句子
        self.input_sentence = ["I", "eat", "an", "apple"]
        self.output_sentence = ["我", "吃", "一个", "苹果"]
        
        # Key向量
        self.key_vectors = {
            "I": [0.1, 0.2, 0.3],
            "eat": [0.4, 0.5, 0.6],
            "an": [0.7, 0.8, 0.9],
            "apple": [1.0, 1.1, 1.2]
        }
        
        # Value向量 (假设V=K简化)
        self.value_vectors = self.key_vectors.copy()
        
        # Query向量
        self.query_vector = [0.9, 1.0, 1.1]
        
        # 对齐分数
        self.scores = [0.358, 0.878, 1.397, 1.917]
        
        # 注意力权重
        self.attention_weights = [0.097, 0.164, 0.275, 0.463]
        
        # 上下文向量
        self.context_vector = [0.7308, 0.8307, 0.9306]
    
    def create_input_output_table(self):
        """创建输入输出句子表格"""
        # 输入句子
        input_data = [
            [Text("英文", font_size=18, weight=BOLD)] + [Text(word, font_size=20) for word in self.input_sentence]
        ]
        
        # 输出句子  
        output_data = [
            [Text("中文", font_size=18, weight=BOLD)] + [Text(word, font_size=20) for word in self.output_sentence]
        ]
        
        # 创建表格
        input_table = MobjectTable(
            input_data,
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        output_table = MobjectTable(
            output_data, 
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        # 高亮当前焦点词
        input_table.get_entries()[4].set_color(YELLOW)  # "apple"
        output_table.get_entries()[4].set_color(YELLOW)  # "苹果"
        
        tables_group = VGroup(input_table, output_table)
        tables_group.arrange(DOWN, buff=0.5)
        
        return tables_group
    
    def create_vectors_table(self):
        """创建向量表格"""
        # 创建Key-Value向量表格
        table_data = [
            [Text("词", font_size=18, weight=BOLD), Text("Key向量", font_size=18, weight=BOLD), Text("Value向量", font_size=18, weight=BOLD)]
        ]
        
        for word in self.input_sentence:
            key_str = f"[{self.key_vectors[word][0]:.1f}, {self.key_vectors[word][1]:.1f}, {self.key_vectors[word][2]:.1f}]"
            value_str = f"[{self.value_vectors[word][0]:.1f}, {self.value_vectors[word][1]:.1f}, {self.value_vectors[word][2]:.1f}]"
            table_data.append([
                Text(word, font_size=16),
                Text(key_str, font_size=14),
                Text(value_str, font_size=14)
            ])
        
        vectors_table = MobjectTable(
            table_data,
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        # Query向量
        query_str = f"[{self.query_vector[0]:.1f}, {self.query_vector[1]:.1f}, {self.query_vector[2]:.1f}]"
        query_text = VGroup(
            Text("Query向量:", font_size=16, color=BLUE),
            Text(query_str, font_size=14, color=BLUE)
        ).arrange(RIGHT, buff=0.2)
        
        return VGroup(vectors_table, query_text).arrange(DOWN, buff=0.3)
    
    def create_score_calculation(self):
        """创建分数计算可视化"""
        # 公式标题
        formula_title = Text("点积缩放公式:", font_size=20, color=WHITE)
        
        # 公式
        formula = MathTex(
            r"Score(Q, K_i) = \frac{Q \cdot K_i}{\sqrt{d_k}}",
            font_size=24
        )
        
        # 计算结果
        calc_results = VGroup()
        for i, word in enumerate(self.input_sentence):
            score_text = Text(f"Score_{word} = {self.scores[i]:.3f}", font_size=16)
            if word == "apple":
                score_text.set_color(YELLOW)
            calc_results.add(score_text)
        
        calc_results.arrange(DOWN, buff=0.2, aligned_edge=LEFT)
        
        # 组合所有元素
        score_group = VGroup(formula_title, formula, calc_results)
        score_group.arrange(DOWN, buff=0.3)
        
        return score_group
    
    def create_attention_weights_visual(self):
        """创建注意力权重可视化"""
        # 权重条形图
        bars = VGroup()
        labels = VGroup()
        
        max_height = 2.0
        bar_width = 0.6
        
        for i, (word, weight) in enumerate(zip(self.input_sentence, self.attention_weights)):
            # 计算条形高度
            height = weight * max_height / max(self.attention_weights)
            
            # 创建条形
            bar = Rectangle(
                width=bar_width, 
                height=height,
                fill_opacity=0.7,
                stroke_color=WHITE
            )
            
            # 根据权重设置颜色
            if word == "apple":
                bar.set_fill(RED)
            else:
                bar.set_fill(BLUE)
            
            bars.add(bar)
            
            # 创建标签
            label = VGroup(
                Text(word, font_size=14),
                Text(f"{weight:.3f}", font_size=12, color=WHITE)
            ).arrange(DOWN, buff=0.1)
            
            labels.add(label)
        
        # 排列条形图
        bars.arrange(RIGHT, buff=0.3)
        
        # 定位标签
        for i, (bar, label) in enumerate(zip(bars, labels)):
            label.next_to(bar, DOWN, buff=0.2)
        
        # Softmax公式
        softmax_formula = MathTex(
            r"Weight_i = \frac{\exp(Score_i)}{\sum_{j=1}^{N} \exp(Score_j)}",
            font_size=20
        )
        
        attention_viz = VGroup(softmax_formula, bars, labels)
        attention_viz.arrange(DOWN, buff=0.4)
        
        return attention_viz
    
    def create_context_calculation(self):
        """创建上下文向量计算"""
        # 公式
        context_formula = MathTex(
            r"Context = \sum_{i=1}^{N} Weight_i \cdot V_i",
            font_size=24
        )
        
        # 计算步骤
        calc_steps = VGroup()
        
        # 展示加权求和
        for i, word in enumerate(self.input_sentence):
            weight = self.attention_weights[i]
            value = self.value_vectors[word]
            step_text = Text(
                f"{weight:.3f} × [{value[0]:.1f}, {value[1]:.1f}, {value[2]:.1f}]",
                font_size=14
            )
            if word == "apple":
                step_text.set_color(YELLOW)
            calc_steps.add(step_text)
        
        calc_steps.arrange(DOWN, buff=0.2, aligned_edge=LEFT)
        
        # 最终结果
        context_str = f"[{self.context_vector[0]:.4f}, {self.context_vector[1]:.4f}, {self.context_vector[2]:.4f}]"
        result_text = VGroup(
            Text("上下文向量 =", font_size=18, color=GREEN),
            Text(context_str, font_size=16, color=GREEN, weight=BOLD)
        ).arrange(RIGHT, buff=0.2)
        
        context_group = VGroup(context_formula, calc_steps, result_text)
        context_group.arrange(DOWN, buff=0.4)
        
        return context_group
    
    def create_final_fusion(self):
        """创建最终融合和翻译"""
        # 融合公式
        fusion_text = Text("融合过程:", font_size=20, color=WHITE)
        
        # Query向量
        query_display = VGroup(
            Text("Query:", font_size=16),
            Text(f"[{self.query_vector[0]:.1f}, {self.query_vector[1]:.1f}, {self.query_vector[2]:.1f}]", font_size=14)
        ).arrange(RIGHT, buff=0.2)
        
        # 上下文向量
        context_display = VGroup(
            Text("Context:", font_size=16),
            Text(f"[{self.context_vector[0]:.4f}, {self.context_vector[1]:.4f}, {self.context_vector[2]:.4f}]", font_size=14)
        ).arrange(RIGHT, buff=0.2)
        
        # 连接符号
        concat_symbol = MathTex(r"\oplus", font_size=24, color=YELLOW)
        
        # 预测结果
        prediction = VGroup(
            Text("预测结果:", font_size=18, color=GREEN),
            Text("苹果 (概率: 0.80)", font_size=16, color=GREEN, weight=BOLD)
        ).arrange(RIGHT, buff=0.2)
        
        fusion_group = VGroup(
            fusion_text,
            query_display,
            concat_symbol,
            context_display,
            prediction
        ).arrange(DOWN, buff=0.3)
        
        return fusion_group
    
    def stage1_introduction(self):
        """阶段1：场景引入与背景设定"""
        # 标题区域
        title_group = self.create_title_region_content("聚焦过程")
        self.play(Write(title_group))
        self.region_elements['title'] = title_group
        
        # 主内容区域 - 输入输出句子
        input_output_table = self.create_input_output_table()
        main_content = VGroup(input_output_table)
        main_region = self.create_main_region_content(main_content)
        self.play(Create(main_region))
        self.region_elements['main'] = main_region
        
        # 左辅助区域
        left_aux = self.create_left_auxiliary_content(
            "任务",
            ["机器翻译", "英文到中文", "生成'苹果'", "注意力机制"]
        )
        self.play(FadeIn(left_aux))
        self.region_elements['left_aux'] = left_aux
        
        # 结果区域
        result_group = self.create_result_region_content("演示：解码器如何'聚焦'到正确的输入词")
        self.play(Write(result_group))
        self.region_elements['result'] = result_group
        
        self.wait(3)
    
    def stage2_calculate_vectors_scores(self):
        """阶段2：步骤1-2：计算向量和对齐分数"""
        # 更新步骤
        step_group = self.create_step_region_content("步骤1-2：计算向量和分数")
        self.play(Write(step_group))
        self.region_elements['step'] = step_group
        
        # 主内容区域 - 向量表格和分数计算
        vectors_table = self.create_vectors_table()
        score_calc = self.create_score_calculation()
        score_calc.scale(0.8)
        
        new_main_content = VGroup(vectors_table, score_calc)
        new_main_content.arrange(RIGHT, buff=0.8)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        right_aux = self.create_right_auxiliary_content(
            "公式",
            ["点积缩放", "维度d_k=3", "相似度计算"]
        )
        self.play(FadeIn(right_aux))
        self.region_elements['right_aux'] = right_aux
        
        # 更新结果
        new_result = self.create_result_region_content("apple获得最高分数1.917，表示最相关")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(4)
    
    def stage3_softmax_normalization(self):
        """阶段3：Softmax归一化"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤3：Softmax归一化")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 注意力权重可视化
        attention_visual = self.create_attention_weights_visual()
        new_main_content = VGroup(attention_visual)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "权重",
            ["apple: 0.463", "an: 0.275", "eat: 0.164", "I: 0.097"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果
        new_result = self.create_result_region_content("注意力权重和为1，apple占最大比重46.3%")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(4)
    
    def stage4_context_vector(self):
        """阶段4：计算上下文向量"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤4：计算上下文向量")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 上下文向量计算
        context_calc = self.create_context_calculation()
        new_main_content = VGroup(context_calc)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "操作",
            ["加权求和", "Value向量", "权重×向量"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果
        new_result = self.create_result_region_content("上下文向量融合了所有输入信息，偏向apple")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(4)
    
    def stage5_generate_translation(self):
        """阶段5：融合生成翻译"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤5：融合生成翻译")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 最终融合
        fusion_visual = self.create_final_fusion()
        new_main_content = VGroup(fusion_visual)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "融合",
            ["Query+Context", "神经网络", "概率预测"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 最终结果
        final_result = self.create_result_region_content("成功生成'苹果'，注意力机制精准聚焦")
        self.play(ReplacementTransform(self.region_elements['result'], final_result))
        self.region_elements['result'] = final_result
        
        # 高亮最终预测
        self.wait(2)
        
        # 创建成功的闪烁效果
        success_rect = SurroundingRectangle(
            fusion_visual[-1],  # 预测结果
            color=GREEN, stroke_width=3
        )
        self.play(Create(success_rect))
        self.play(Flash(success_rect, flash_radius=0.5, color=GREEN))
        
        self.wait(4)

# 运行场景的方法
if __name__ == "__main__":
    scene = AttentionMechanismScene()
    scene.render() 