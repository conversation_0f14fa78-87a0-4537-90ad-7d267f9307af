#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频剪辑Agent

该Agent具备以下功能：
1. 通过yt-dlp下载视频和对应的字幕内容
2. 根据purpose对字幕做语义加工生成
3. 给出对应字幕时间戳范围，用来截取视频内容作为新视频的素材
4. 可选：自动剪辑视频片段
"""

import argparse
import datetime
import json
import logging
import os
import re
import subprocess
import sys
import tempfile
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.messages import BaseMessage
from utils.common import AgentFactory, Config

# 配置日志
logger = logging.getLogger(__name__)

class SubtitleSegment:
    """字幕片段类"""
    def __init__(self, start_time: float, end_time: float, text: str):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.duration = end_time - start_time
    
    def __repr__(self):
        return f"SubtitleSegment({self.start_time:.2f}-{self.end_time:.2f}: {self.text[:50]}...)"

class VideoCutAgent:
    def __init__(self, config_path="config/config.yaml"):
        """初始化视频剪辑Agent"""
        self.config_path = config_path
        self.load_config()
        
        # 初始化模型
        self.config = Config(config_path)
        self.model = AgentFactory.create_model(self.config)
        
        # 创建不同功能的分析代理
        self.content_analyzer = AgentFactory.create_analyzer_agent(
            self.model,
            "字幕内容分析专家",
            "你是一个专业的字幕内容分析专家，能够根据指定目的分析字幕内容，识别相关片段并生成时间戳。"
        )
        
        self.segment_evaluator = AgentFactory.create_analyzer_agent(
            self.model,
            "视频片段评估专家", 
            "你是一个专业的视频片段评估专家，能够评估字幕片段的相关性和价值，并提供剪辑建议。"
        )
        
        # 检查yt-dlp是否安装
        self.check_ytdlp()
        
        logger.info("视频剪辑Agent初始化完成")

    def check_ytdlp(self):
        """检查yt-dlp是否已安装"""
        try:
            result = subprocess.run(['yt-dlp', '--version'], 
                                 capture_output=True, text=True, check=True)
            logger.info(f"yt-dlp版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("yt-dlp未安装或无法访问，请运行: pip install yt-dlp")
            raise Exception("yt-dlp未安装，请先安装: pip install yt-dlp")

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取medias配置
            material_config = config.get("material", {})
            sources_config = material_config.get("sources", {})
            self.medias_config = sources_config.get("medias", {})
            
            # 默认配置
            default_config = {
                "enabled": False,
                "url": "",
                "purpose": "提取视频核心内容",
                "download_config": {
                    "video_quality": "best[height<=720]",
                    "audio_quality": "best",
                    "download_subtitles": True,
                    "subtitle_languages": ["zh", "en", "auto"],
                    "output_format": "mp4"
                },
                "subtitle_analysis": {
                    "segment_min_duration": 10,
                    "segment_max_duration": 180,
                    "context_window": 30,
                    "relevance_threshold": 0.7
                },
                "video_cutting": {
                    "enable_auto_cut": True,
                    "fade_duration": 1.0,
                    "min_gap": 2.0,
                    "output_segments": True
                }
            }
            
            # 合并默认配置
            for key, value in default_config.items():
                if key not in self.medias_config:
                    self.medias_config[key] = value
                elif isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if sub_key not in self.medias_config[key]:
                            self.medias_config[key][sub_key] = sub_value
                    
            logger.info(f"媒体剪辑配置加载完成")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            # 使用默认配置
            self.medias_config = {
                "enabled": False,
                "url": "",
                "purpose": "提取视频核心内容"
            }

    def download_video_and_subtitles(self, url: str, output_dir: str) -> Dict[str, str]:
        """下载视频和字幕"""
        logger.info(f"开始下载视频: {url}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        download_config = self.medias_config.get("download_config", {})
        
        # 构建yt-dlp命令
        cmd = [
            'yt-dlp',
            '--format', download_config.get("video_quality", "best[height<=720]"),
            '--output', os.path.join(output_dir, '%(title)s.%(ext)s'),
        ]
        
        # 添加字幕下载选项
        if download_config.get("download_subtitles", True):
            cmd.extend([
                '--write-subs',
                '--write-auto-subs',
                '--sub-langs', ','.join(download_config.get("subtitle_languages", ["zh", "en", "auto"])),
                '--convert-subs', 'srt'
            ])
        
        # 添加URL
        cmd.append(url)
        
        try:
            # 执行下载
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, cwd=output_dir)
            logger.info(f"视频下载完成")
            
            # 查找下载的文件
            video_files = []
            subtitle_files = []
            
            for file in os.listdir(output_dir):
                if file.endswith(('.mp4', '.mkv', '.webm', '.avi')):
                    video_files.append(os.path.join(output_dir, file))
                elif file.endswith('.srt'):
                    subtitle_files.append(os.path.join(output_dir, file))
            
            if not video_files:
                raise Exception("未找到下载的视频文件")
            
            # 返回主要文件路径
            result_files = {
                "video": video_files[0] if video_files else None,
                "subtitles": subtitle_files
            }
            
            logger.info(f"下载结果: 视频={result_files['video']}, 字幕={len(subtitle_files)}个")
            return result_files
            
        except subprocess.CalledProcessError as e:
            logger.error(f"视频下载失败: {e}")
            logger.error(f"错误输出: {e.stderr}")
            raise Exception(f"视频下载失败: {e.stderr}")

    def parse_srt_file(self, srt_path: str) -> List[SubtitleSegment]:
        """解析SRT字幕文件"""
        logger.info(f"解析字幕文件: {srt_path}")
        
        segments = []
        
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按空行分割字幕块
            blocks = re.split(r'\n\s*\n', content.strip())
            
            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 解析时间戳
                time_line = lines[1]
                time_match = re.match(r'(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})', time_line)
                
                if not time_match:
                    continue
                
                # 转换为秒
                start_time = (int(time_match.group(1)) * 3600 + 
                            int(time_match.group(2)) * 60 + 
                            int(time_match.group(3)) + 
                            int(time_match.group(4)) / 1000)
                
                end_time = (int(time_match.group(5)) * 3600 + 
                           int(time_match.group(6)) * 60 + 
                           int(time_match.group(7)) + 
                           int(time_match.group(8)) / 1000)
                
                # 合并字幕文本
                text = ' '.join(lines[2:]).strip()
                
                # 清理HTML标签和特殊字符
                text = re.sub(r'<[^>]+>', '', text)
                text = re.sub(r'\s+', ' ', text)
                
                if text:
                    segments.append(SubtitleSegment(start_time, end_time, text))
            
            logger.info(f"解析完成，共 {len(segments)} 个字幕片段")
            return segments
            
        except Exception as e:
            logger.error(f"解析字幕文件失败: {e}")
            return []

    def merge_segments(self, segments: List[SubtitleSegment], max_duration: float = 30) -> List[SubtitleSegment]:
        """合并连续的字幕片段"""
        if not segments:
            return []
        
        merged = []
        current_start = segments[0].start_time
        current_end = segments[0].end_time
        current_text = segments[0].text
        
        for segment in segments[1:]:
            # 如果时间间隔小于2秒且总时长不超过最大时长，则合并
            if (segment.start_time - current_end <= 2.0 and 
                segment.end_time - current_start <= max_duration):
                current_end = segment.end_time
                current_text += " " + segment.text
            else:
                # 保存当前合并的片段
                merged.append(SubtitleSegment(current_start, current_end, current_text))
                # 开始新的片段
                current_start = segment.start_time
                current_end = segment.end_time
                current_text = segment.text
        
        # 添加最后一个片段
        merged.append(SubtitleSegment(current_start, current_end, current_text))
        
        return merged

    def analyze_subtitle_relevance(self, segments: List[SubtitleSegment], purpose: str) -> List[Dict[str, Any]]:
        """分析字幕片段与目的的相关性"""
        logger.info(f"分析字幕相关性，目的: {purpose}")
        
        # 合并短片段
        merged_segments = self.merge_segments(segments)
        
        if not merged_segments:
            return []
        
        # 构建分析提示
        analysis_prompt = f"""
请分析以下字幕片段与指定目的的相关性。

**分析目的**: {purpose}

**分析要求**:
1. 评估每个字幕片段与目的的相关性(0-1分)
2. 识别最有价值的内容片段
3. 提供选择理由和建议
4. 考虑内容的完整性和逻辑性

**字幕片段**:
"""
        
        # 添加字幕片段信息
        for i, segment in enumerate(merged_segments[:50]):  # 限制片段数量
            analysis_prompt += f"""
片段 {i+1}: [{self.format_time(segment.start_time)} - {self.format_time(segment.end_time)}] ({segment.duration:.1f}秒)
内容: {segment.text}

"""
        
        analysis_prompt += """
请以JSON格式返回分析结果，格式如下：
{
    "summary": "整体分析总结",
    "segments": [
        {
            "index": 1,
            "start_time": 开始时间(秒),
            "end_time": 结束时间(秒),
            "relevance_score": 相关性分数(0-1),
            "reason": "选择理由",
            "content_summary": "内容摘要",
            "recommended": true/false
        }
    ],
    "recommended_clips": [
        {
            "start_time": 开始时间(秒),
            "end_time": 结束时间(秒),
            "title": "片段标题",
            "description": "片段描述",
            "priority": "high/medium/low"
        }
    ]
}
"""
        
        try:
            # 调用AI分析
            response = self.content_analyzer.step(
                BaseMessage.make_user_message(
                    role_name="User",
                    content=analysis_prompt
                )
            )
            
            result_text = response.msg.content
            
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if json_match:
                try:
                    result_json = json.loads(json_match.group())
                    logger.info(f"字幕分析完成，识别出 {len(result_json.get('segments', []))} 个片段")
                    return result_json
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {e}")
            
            # 如果JSON解析失败，返回基础结果
            threshold = self.medias_config.get("subtitle_analysis", {}).get("relevance_threshold", 0.7)
            basic_result = {
                "summary": "基础分析结果",
                "segments": [],
                "recommended_clips": []
            }
            
            # 简单的关键词匹配作为备选方案
            purpose_keywords = purpose.lower().split()
            for i, segment in enumerate(merged_segments):
                score = 0
                text_lower = segment.text.lower()
                for keyword in purpose_keywords:
                    if keyword in text_lower:
                        score += 0.2
                
                score = min(score, 1.0)
                
                if score >= threshold:
                    basic_result["segments"].append({
                        "index": i + 1,
                        "start_time": segment.start_time,
                        "end_time": segment.end_time,
                        "relevance_score": score,
                        "reason": "关键词匹配",
                        "content_summary": segment.text[:100] + "...",
                        "recommended": True
                    })
            
            return basic_result
            
        except Exception as e:
            logger.error(f"字幕分析失败: {e}")
            return {
                "summary": f"分析失败: {str(e)}",
                "segments": [],
                "recommended_clips": []
            }

    def format_time(self, seconds: float) -> str:
        """格式化时间为 HH:MM:SS 格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def generate_cutting_plan(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成视频剪辑方案"""
        logger.info("生成视频剪辑方案")
        
        cutting_config = self.medias_config.get("video_cutting", {})
        min_duration = self.medias_config.get("subtitle_analysis", {}).get("segment_min_duration", 10)
        max_duration = self.medias_config.get("subtitle_analysis", {}).get("segment_max_duration", 180)
        min_gap = cutting_config.get("min_gap", 2.0)
        
        # 获取推荐片段
        recommended_clips = analysis_result.get("recommended_clips", [])
        high_score_segments = [
            seg for seg in analysis_result.get("segments", [])
            if seg.get("recommended", False) and seg.get("relevance_score", 0) >= 0.7
        ]
        
        cutting_plan = {
            "total_clips": len(recommended_clips) + len(high_score_segments),
            "estimated_duration": 0,
            "clips": [],
            "segments": [],
            "cutting_commands": []
        }
        
        # 处理推荐片段
        for i, clip in enumerate(recommended_clips):
            duration = clip["end_time"] - clip["start_time"]
            if min_duration <= duration <= max_duration:
                cutting_plan["clips"].append({
                    "id": f"clip_{i+1}",
                    "start_time": clip["start_time"],
                    "end_time": clip["end_time"],
                    "duration": duration,
                    "title": clip.get("title", f"片段 {i+1}"),
                    "description": clip.get("description", ""),
                    "priority": clip.get("priority", "medium"),
                    "output_file": f"clip_{i+1}_{self.format_time(clip['start_time']).replace(':', '')}.mp4"
                })
                cutting_plan["estimated_duration"] += duration
        
        # 处理高分字幕片段
        for i, segment in enumerate(high_score_segments):
            duration = segment["end_time"] - segment["start_time"]
            if min_duration <= duration <= max_duration:
                cutting_plan["segments"].append({
                    "id": f"segment_{i+1}",
                    "start_time": segment["start_time"],
                    "end_time": segment["end_time"],
                    "duration": duration,
                    "relevance_score": segment["relevance_score"],
                    "reason": segment["reason"],
                    "content_summary": segment["content_summary"],
                    "output_file": f"segment_{i+1}_{self.format_time(segment['start_time']).replace(':', '')}.mp4"
                })
                cutting_plan["estimated_duration"] += duration
        
        logger.info(f"剪辑方案生成完成: {cutting_plan['total_clips']} 个片段，预计时长 {cutting_plan['estimated_duration']:.1f} 秒")
        return cutting_plan

    def cut_video_segments(self, video_path: str, cutting_plan: Dict[str, Any], output_dir: str) -> List[str]:
        """剪辑视频片段"""
        if not self.medias_config.get("video_cutting", {}).get("enable_auto_cut", True):
            logger.info("自动剪辑功能已禁用")
            return []
        
        logger.info(f"开始剪辑视频片段: {video_path}")
        
        # 检查ffmpeg是否可用
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("ffmpeg未安装或无法访问，跳过视频剪辑")
            return []
        
        output_files = []
        cutting_config = self.medias_config.get("video_cutting", {})
        fade_duration = cutting_config.get("fade_duration", 1.0)
        
        # 剪辑推荐片段
        for clip in cutting_plan.get("clips", []):
            output_path = os.path.join(output_dir, clip["output_file"])
            
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(clip["start_time"]),
                '-t', str(clip["duration"]),
                '-vf', f'fade=in:0:{fade_duration},fade=out:{clip["duration"]-fade_duration}:{fade_duration}',
                '-af', f'afade=in:st=0:d={fade_duration},afade=out:st={clip["duration"]-fade_duration}:d={fade_duration}',
                '-c:v', 'libx264',
                '-c:a', 'aac',
                output_path
            ]
            
            try:
                subprocess.run(cmd, capture_output=True, check=True)
                output_files.append(output_path)
                logger.info(f"片段剪辑完成: {clip['output_file']}")
            except subprocess.CalledProcessError as e:
                logger.error(f"片段剪辑失败: {clip['output_file']}, 错误: {e}")
        
        # 剪辑字幕片段
        for segment in cutting_plan.get("segments", []):
            output_path = os.path.join(output_dir, segment["output_file"])
            
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(segment["start_time"]),
                '-t', str(segment["duration"]),
                '-vf', f'fade=in:0:{fade_duration},fade=out:{segment["duration"]-fade_duration}:{fade_duration}',
                '-af', f'afade=in:st=0:d={fade_duration},afade=out:st={segment["duration"]-fade_duration}:d={fade_duration}',
                '-c:v', 'libx264',
                '-c:a', 'aac',
                output_path
            ]
            
            try:
                subprocess.run(cmd, capture_output=True, check=True)
                output_files.append(output_path)
                logger.info(f"片段剪辑完成: {segment['output_file']}")
            except subprocess.CalledProcessError as e:
                logger.error(f"片段剪辑失败: {segment['output_file']}, 错误: {e}")
        
        logger.info(f"视频剪辑完成，生成 {len(output_files)} 个片段")
        return output_files

    def process_media(self, url: str = None, purpose: str = None) -> Dict[str, Any]:
        """处理媒体的主要入口函数"""
        # 使用配置或参数
        url = url or self.medias_config.get("url", "")
        purpose = purpose or self.medias_config.get("purpose", "提取视频核心内容")
        
        if not url:
            raise ValueError("未指定视频URL")
        
        logger.info(f"开始处理媒体: {url}")
        logger.info(f"处理目的: {purpose}")
        
        # 创建输出目录
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join("output", f"media_cut_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        
        results = {
            "url": url,
            "purpose": purpose,
            "output_dir": output_dir,
            "download_files": {},
            "subtitle_analysis": {},
            "cutting_plan": {},
            "output_segments": [],
            "errors": []
        }
        
        try:
            # 1. 下载视频和字幕
            logger.info("步骤1: 下载视频和字幕")
            download_files = self.download_video_and_subtitles(url, output_dir)
            results["download_files"] = download_files
            
            if not download_files.get("video"):
                raise Exception("视频下载失败")
            
            # 2. 解析字幕
            logger.info("步骤2: 解析字幕文件")
            all_segments = []
            
            for subtitle_file in download_files.get("subtitles", []):
                segments = self.parse_srt_file(subtitle_file)
                all_segments.extend(segments)
            
            if not all_segments:
                logger.warning("未找到可用的字幕内容")
                results["errors"].append("未找到可用的字幕内容")
                return results
            
            # 3. 分析字幕相关性
            logger.info("步骤3: 分析字幕相关性")
            analysis_result = self.analyze_subtitle_relevance(all_segments, purpose)
            results["subtitle_analysis"] = analysis_result
            
            # 保存分析结果
            analysis_file = os.path.join(output_dir, "subtitle_analysis.json")
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            # 4. 生成剪辑方案
            logger.info("步骤4: 生成剪辑方案")
            cutting_plan = self.generate_cutting_plan(analysis_result)
            results["cutting_plan"] = cutting_plan
            
            # 保存剪辑方案
            plan_file = os.path.join(output_dir, "cutting_plan.json")
            with open(plan_file, 'w', encoding='utf-8') as f:
                json.dump(cutting_plan, f, ensure_ascii=False, indent=2)
            
            # 5. 剪辑视频片段（可选）
            if self.medias_config.get("video_cutting", {}).get("output_segments", True):
                logger.info("步骤5: 剪辑视频片段")
                output_segments = self.cut_video_segments(
                    download_files["video"], 
                    cutting_plan, 
                    output_dir
                )
                results["output_segments"] = output_segments
            
            # 6. 生成汇总报告
            self.generate_summary_report(results, output_dir)
            
            logger.info(f"媒体处理完成，结果保存在: {output_dir}")
            return results
            
        except Exception as e:
            logger.error(f"媒体处理失败: {e}")
            results["errors"].append(str(e))
            return results

    def generate_summary_report(self, results: Dict[str, Any], output_dir: str):
        """生成汇总报告"""
        report_content = f"""# 视频剪辑分析报告

## 基本信息
- **视频URL**: {results['url']}
- **处理目的**: {results['purpose']}
- **处理时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **输出目录**: {results['output_dir']}

## 下载结果
- **视频文件**: {results['download_files'].get('video', '未下载')}
- **字幕文件**: {len(results['download_files'].get('subtitles', []))} 个

## 字幕分析
{results['subtitle_analysis'].get('summary', '未分析')}

### 相关片段统计
- **总片段数**: {len(results['subtitle_analysis'].get('segments', []))}
- **推荐片段数**: {len([s for s in results['subtitle_analysis'].get('segments', []) if s.get('recommended', False)])}

## 剪辑方案
- **计划片段数**: {results['cutting_plan'].get('total_clips', 0)}
- **预计总时长**: {results['cutting_plan'].get('estimated_duration', 0):.1f} 秒

### 推荐片段
"""
        
        # 添加推荐片段详情
        for clip in results['cutting_plan'].get('clips', []):
            report_content += f"""
#### {clip['title']}
- **时间范围**: {self.format_time(clip['start_time'])} - {self.format_time(clip['end_time'])}
- **时长**: {clip['duration']:.1f} 秒
- **优先级**: {clip['priority']}
- **描述**: {clip['description']}
"""
        
        # 添加错误信息
        if results.get('errors'):
            report_content += f"""
## 错误信息
"""
            for error in results['errors']:
                report_content += f"- {error}\n"
        
        # 保存报告
        report_file = os.path.join(output_dir, "summary_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"汇总报告已生成: {report_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="视频剪辑Agent")
    parser.add_argument("--url", help="视频URL")
    parser.add_argument("--purpose", help="处理目的")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--test", action="store_true", help="运行测试用例")
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"output/video_cut_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
            logging.StreamHandler()
        ]
    )
    
    try:
        # 初始化Agent
        agent = VideoCutAgent(args.config)
        
        if args.test:
            # 测试用例
            test_url = "https://www.youtube.com/watch?v=5QcCeSsNRks"  # 示例视频
            test_purpose = "提取视频中核心讲解内容和结论，并剪辑制作成视频，视频长度3分钟，视频内容要震撼，有冲击力"
            
            print(f"🧪 运行测试用例")
            print(f"🔗 测试URL: {test_url}")
            print(f"🎯 测试目的: {test_purpose}")
            
            results = agent.process_media(test_url, test_purpose)
        else:
            # 使用参数或配置
            url = args.url
            purpose = args.purpose
            
            if not url and not agent.medias_config.get("enabled", False):
                print("❌ 请指定视频URL (--url) 或在配置中启用medias功能")
                parser.print_help()
                return
            
            results = agent.process_media(url, purpose)
        
        # 输出结果
        print(f"\n✅ 媒体处理完成！")
        print(f"📊 处理结果:")
        print(f"  📁 输出目录: {results['output_dir']}")
        print(f"  🎬 视频文件: {results['download_files'].get('video', '未下载')}")
        print(f"  📝 字幕文件: {len(results['download_files'].get('subtitles', []))} 个")
        print(f"  ✂️ 剪辑片段: {len(results['output_segments'])} 个")
        
        if results.get('errors'):
            print(f"  ⚠️ 错误: {len(results['errors'])} 个")
            for error in results['errors']:
                print(f"    - {error}")
        
    except Exception as e:
        logger.error(f"视频剪辑失败: {e}")
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 