from manim import *
from prompts.professional_science_template import ProfessionalScienceTemplate
import numpy as np

class Word2VecSemanticMagicScene(ProfessionalScienceTemplate):
    """Word2Vec语义魔法专业教学场景"""
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 初始化数据
        self.init_data()
        
        # 执行各个阶段
        self.stage1_introduction()
        self.stage2_build_vocabulary()
        self.stage3_generate_samples()
        self.stage4_training_logic()
        self.stage5_final_application()
    
    def init_data(self):
        """初始化数据"""
        # 语料库
        self.corpus = [
            "香蕉 橘子 苹果",
            "橘子 苹果 梨",
            "香蕉 苹果 梨 橘子"
        ]
        
        # 词典
        self.vocabulary = ["香蕉", "橘子", "苹果", "梨"]
        
        # 初始词向量 (简化为3维)
        self.initial_vectors = {
            "香蕉": [0.2, -0.1, 0.3],
            "橘子": [0.1, 0.4, -0.2],
            "苹果": [-0.3, 0.2, 0.1],
            "梨": [0.4, -0.3, 0.2]
        }
        
        # 最终词向量 (训练后)
        self.final_vectors = {
            "香蕉": [0.5, 0.3, 0.4],
            "橘子": [0.4, 0.4, 0.3],
            "苹果": [0.2, 0.3, 0.2],
            "梨": [0.1, 0.2, 0.4]
        }
    
    def create_vocabulary_table(self, vectors_dict):
        """创建词典表格"""
        # 创建表格数据，包含标题行
        table_data = [
            [Text("词", font_size=20, weight=BOLD), Text("向量", font_size=20, weight=BOLD)]
        ]
        
        for word, vector in vectors_dict.items():
            vector_str = f"[{vector[0]:.1f}, {vector[1]:.1f}, {vector[2]:.1f}]"
            table_data.append([Text(word, font_size=24), Text(vector_str, font_size=20)])
        
        # 创建表格
        table = MobjectTable(
            table_data,
            include_outer_lines=True,
            h_buff=0.5,
            v_buff=0.3,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        return table
    
    def create_sample_generation_figure(self):
        """创建样本生成图示"""
        # 创建句子
        sentence = "香蕉 橘子 苹果"
        words = sentence.split()
        
        # 创建词语方块
        word_squares = []
        for i, word in enumerate(words):
            square = Square(side_length=1.0, color=WHITE, fill_opacity=0.1)
            text = Text(word, font_size=24, color=WHITE)
            text.move_to(square.get_center())
            word_group = VGroup(square, text)
            word_squares.append(word_group)
        
        # 排列词语
        sentence_group = VGroup(*word_squares)
        sentence_group.arrange(RIGHT, buff=0.2)
        
        # 创建窗口指示
        window_rect = Rectangle(
            width=2.4, height=1.2,
            color=YELLOW, stroke_width=3, fill_opacity=0.1
        )
        window_rect.move_to(word_squares[1].get_center())  # 窗口在"橘子"上
        
        # 创建箭头和标签
        center_arrow = Arrow(
            start=word_squares[1].get_bottom() + DOWN * 0.5,
            end=word_squares[1].get_bottom() + DOWN * 0.1,
            color=RED
        )
        center_label = Text("中心词", font_size=18, color=RED)
        center_label.next_to(center_arrow, DOWN)
        
        context_arrows = VGroup()
        context_labels = VGroup()
        
        # 上下文词箭头
        for i in [0, 2]:  # "香蕉"和"苹果"
            arrow = Arrow(
                start=word_squares[i].get_top() + UP * 0.5,
                end=word_squares[i].get_top() + UP * 0.1,
                color=BLUE
            )
            label = Text("上下文", font_size=16, color=BLUE)
            label.next_to(arrow, UP)
            context_arrows.add(arrow)
            context_labels.add(label)
        
        return VGroup(
            sentence_group, window_rect, center_arrow, center_label,
            context_arrows, context_labels
        )
    
    def create_training_logic_figure(self):
        """创建训练逻辑图示"""
        # 创建公式
        formula_title = Text("训练逻辑：", font_size=24, color=WHITE)
        
        # 点积公式
        dot_product = VGroup(
            Text("相似度 = ", font_size=24, color=WHITE),
            MathTex(r"\vec{v}_{banana} \cdot \vec{v}_{orange}", font_size=24)
        ).arrange(RIGHT, buff=0.1)
        
        # 更新公式 (简化)
        update_formula = MathTex(
            r"\vec{v}_{new} = \vec{v}_{old} + \alpha \cdot \Delta",
            font_size=28
        )
        
        # 向量示意图
        banana_vector = Arrow(ORIGIN, [1.5, 0.8, 0], color=YELLOW, buff=0)
        banana_label = Text("香蕉", font_size=18, color=YELLOW)
        banana_label.next_to(banana_vector.get_end(), RIGHT)
        
        orange_vector = Arrow(ORIGIN, [1.2, 1.0, 0], color=ORANGE, buff=0)
        orange_label = Text("橘子", font_size=18, color=ORANGE)
        orange_label.next_to(orange_vector.get_end(), RIGHT)
        
        # 调整箭头
        adjustment_arrow = CurvedArrow(
            start_point=banana_vector.get_end(),
            end_point=orange_vector.get_end(),
            color=GREEN
        )
        adjustment_label = Text("调整", font_size=16, color=GREEN)
        adjustment_label.next_to(adjustment_arrow, UP)
        
        # 组织所有元素
        formulas = VGroup(formula_title, dot_product, update_formula)
        formulas.arrange(DOWN, buff=0.3)
        
        vectors = VGroup(
            banana_vector, banana_label, orange_vector, orange_label,
            adjustment_arrow, adjustment_label
        )
        
        main_group = VGroup(formulas, vectors)
        main_group.arrange(DOWN, buff=0.5)
        
        return main_group
    
    def create_similarity_calculation(self):
        """创建相似度计算"""
        # 香蕉和橘子的相似度
        banana_orange_sim = VGroup(
            Text("相似度(香蕉, 橘子) = ", font_size=20, color=WHITE),
            Text("0.85", font_size=20, color=GREEN, weight=BOLD)
        ).arrange(RIGHT, buff=0.1)
        
        # 香蕉和苹果的相似度
        banana_apple_sim = VGroup(
            Text("相似度(香蕉, 苹果) = ", font_size=20, color=WHITE),
            Text("0.62", font_size=20, color=YELLOW, weight=BOLD)
        ).arrange(RIGHT, buff=0.1)
        
        # 结论
        conclusion = Text(
            "香蕉与橘子更相似", 
            font_size=20, color=WHITE
        )
        
        calc_group = VGroup(banana_orange_sim, banana_apple_sim, conclusion)
        calc_group.arrange(DOWN, buff=0.3)
        
        return calc_group
    
    def stage1_introduction(self):
        """阶段1：场景引入与背景设定"""
        # 标题区域
        title_group = self.create_title_region_content("语义魔法")
        self.play(Write(title_group))
        self.region_elements['title'] = title_group
        
        # 主内容区域 - 介绍
        intro_text = Text(
            "水果店的智能推荐系统\n背景：少量顾客购买记录",
            font_size=28, color=WHITE
        )
        main_content = VGroup(intro_text)
        main_region = self.create_main_region_content(main_content)
        self.play(FadeIn(main_region))
        self.region_elements['main'] = main_region
        
        # 左辅助区域 - 语料库
        corpus_items = []
        for i, sentence in enumerate(self.corpus):
            corpus_items.append(f"记录{i+1}：{sentence}")
        
        left_aux = self.create_left_auxiliary_content(
            "语料库", corpus_items
        )
        self.play(FadeIn(left_aux))
        self.region_elements['left_aux'] = left_aux
        
        self.wait(3)
    
    def stage2_build_vocabulary(self):
        """阶段2：步骤1：构建词典并初始化词向量"""
        # 步骤区域
        step_group = self.create_step_region_content("步骤1：构建词典初始化向量")
        self.play(Write(step_group))
        self.region_elements['step'] = step_group
        
        # 主内容区域 - 词典表格
        dict_table = self.create_vocabulary_table(self.initial_vectors)
        new_main_content = VGroup(dict_table)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 右辅助区域
        right_aux = self.create_right_auxiliary_content(
            "操作",
            ["找出不重复词", "随机生成向量"]
        )
        self.play(FadeIn(right_aux))
        self.region_elements['right_aux'] = right_aux
        
        # 结果区域
        result_group = self.create_result_region_content("应用：为所有词创建'数字指纹'")
        self.play(Write(result_group))
        self.region_elements['result'] = result_group
        
        self.wait(3)
    
    def stage3_generate_samples(self):
        """阶段3：步骤2：生成"中心词-上下文词"对"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤2：生成中心词上下文词对")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 样本生成图示
        sample_gen_figure = self.create_sample_generation_figure()
        new_main_content = VGroup(sample_gen_figure)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "操作",
            ["设定窗口", "生成训练样本"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果区域
        new_result = self.create_result_region_content("应用：词语'朋友圈'关系，模型学习这些关系")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(3)
    
    def stage4_training_logic(self):
        """阶段4：步骤3：模型训练与词向量更新"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤3：模型训练词向量更新")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 隐藏左辅助区域
        self.play(FadeOut(self.region_elements['left_aux']))
        
        # 主内容区域 - 训练逻辑
        training_logic = self.create_training_logic_figure()
        new_main_content = VGroup(training_logic)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "操作",
            ["通过简化神经网络训练", "调整向量"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果区域
        new_result = self.create_result_region_content("应用：词向量更能反映上下文信息，相似词向量更接近")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(4)
    
    def stage5_final_application(self):
        """阶段5：步骤N：得到最终词向量并实际应用"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤N：得到最终词向量应用")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 最终词向量表格
        final_table = self.create_vocabulary_table(self.final_vectors)
        
        # 添加相似度计算
        similarity_calc = self.create_similarity_calculation()
        similarity_calc.scale(0.8)
        similarity_calc.next_to(final_table, RIGHT, buff=0.5)
        
        new_main_content = VGroup(final_table, similarity_calc)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "操作",
            ["导出训练好的词向量", "应用于相似词查找"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 最终推荐结果
        final_result = self.create_result_region_content("顾客购买'香蕉'，推荐'橘子'")
        self.play(ReplacementTransform(self.region_elements['result'], final_result))
        self.region_elements['result'] = final_result
        
        # 高亮推荐逻辑
        self.wait(2)
        
        # 最终高亮效果
        highlight_rect = SurroundingRectangle(
            similarity_calc[0],  # 香蕉橘子相似度
            color=GREEN, stroke_width=3
        )
        self.play(Create(highlight_rect))
        self.play(Flash(highlight_rect, flash_radius=0.5))
        
        self.wait(4)

# 运行场景的方法
if __name__ == "__main__":
    scene = Word2VecSemanticMagicScene()
    scene.render() 