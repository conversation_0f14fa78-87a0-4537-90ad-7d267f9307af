#!/usr/bin/env python3
"""
快速排序演示运行脚本
"""

import subprocess
import sys
import os

def run_quicksort_demo():
    """运行快速排序演示"""
    
    print("🚀 开始运行快速排序演示...")
    print("=" * 50)
    
    try:
        # 确保在正确的目录中
        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")
        
        # 运行manim命令
        cmd = [
            "manim",
            "quicksort_demo.py", 
            "QuickSortDemo",
            "-pql"  # 预览，质量低，显示最后一帧
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 50)
        
        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 快速排序演示运行成功！")
            print("\n📹 视频输出信息:")
            print(result.stdout)
            
            # 查找生成的视频文件
            media_dir = "media"
            if os.path.exists(media_dir):
                print(f"\n📁 媒体文件目录: {media_dir}")
                for root, dirs, files in os.walk(media_dir):
                    for file in files:
                        if file.endswith('.mp4'):
                            video_path = os.path.join(root, file)
                            print(f"🎬 生成的视频: {video_path}")
            
        else:
            print("❌ 运行失败!")
            print(f"错误代码: {result.returncode}")
            print(f"错误信息:\n{result.stderr}")
            
            # 常见错误的解决建议
            if "ModuleNotFoundError" in result.stderr:
                print("\n💡 解决建议:")
                print("1. 确保已安装manim: pip install manim")
                print("2. 确保在正确的Python环境中")
                
            if "professional_science_template" in result.stderr:
                print("\n💡 解决建议:")
                print("1. 确保professional_science_template.py在prompts目录中")
                print("2. 检查文件路径是否正确")
                
    except FileNotFoundError:
        print("❌ 找不到manim命令!")
        print("💡 请先安装manim: pip install manim")
        return False
        
    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        return False
    
    return result.returncode == 0

def check_prerequisites():
    """检查运行prerequisites"""
    print("🔍 检查运行环境...")
    
    # 检查manim是否安装
    try:
        result = subprocess.run(["manim", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Manim版本: {result.stdout.strip()}")
        else:
            print("❌ Manim未正确安装")
            return False
    except FileNotFoundError:
        print("❌ 找不到manim命令")
        return False
    
    # 检查必要文件
    files_to_check = [
        "quicksort_demo.py",
        "prompts/professional_science_template.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ 找到文件: {file_path}")
        else:
            print(f"❌ 缺少文件: {file_path}")
            return False
    
    return True

def main():
    """主函数"""
    print("🎯 快速排序演示启动器")
    print("=" * 50)
    
    # 检查prerequisites
    if not check_prerequisites():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 运行演示
    success = run_quicksort_demo()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 快速排序演示完成！")
        print("\n📖 演示内容包括:")
        print("• 步骤1: 初始化数组 [64,34,25,12,22,11,90]")
        print("• 步骤2: 选择基准值 (90)")
        print("• 步骤3: 分区操作")
        print("• 步骤4: 递归排序")
        print("• 步骤5: 最终结果 [11,12,22,25,34,64,90]")
        print("\n🎨 特色功能:")
        print("• 专业的5区域布局")
        print("• 彩色数组可视化")
        print("• 逐步动画展示")
        print("• 算法复杂度分析")
        print("• 庆祝效果")
    else:
        print("❌ 演示运行失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 