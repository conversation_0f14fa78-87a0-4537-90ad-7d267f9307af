# labeled

Mobjects that inherit from lines and contain a label along the length.

### Classes

| [`Label`](manim.mobject.geometry.labeled.Label.md#manim.mobject.geometry.labeled.Label)                               | A Label consisting of text surrounded by a frame.                            |
|-----------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------|
| [`LabeledArrow`](manim.mobject.geometry.labeled.LabeledArrow.md#manim.mobject.geometry.labeled.LabeledArrow)          | Constructs an arrow containing a label box somewhere along its length.       |
| [`LabeledLine`](manim.mobject.geometry.labeled.LabeledLine.md#manim.mobject.geometry.labeled.LabeledLine)             | Constructs a line containing a label box somewhere along its length.         |
| [`LabeledPolygram`](manim.mobject.geometry.labeled.LabeledPolygram.md#manim.mobject.geometry.labeled.LabeledPolygram) | Constructs a polygram containing a label box at its pole of inaccessibility. |
