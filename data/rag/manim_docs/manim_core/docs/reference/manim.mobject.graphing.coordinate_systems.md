# coordinate_systems

Mobjects that represent coordinate systems.

### TypeVar’s

### *class* LineType

```default
TypeVar('LineType', bound=Line)
```

### Classes

| [`Axes`](manim.mobject.graphing.coordinate_systems.Axes.md#manim.mobject.graphing.coordinate_systems.Axes)                                     | Creates a set of axes.                                                                                                                                                      |
|------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`ComplexPlane`](manim.mobject.graphing.coordinate_systems.ComplexPlane.md#manim.mobject.graphing.coordinate_systems.ComplexPlane)             | A [`NumberPlane`](manim.mobject.graphing.coordinate_systems.NumberPlane.md#manim.mobject.graphing.coordinate_systems.NumberPlane) specialized for use with complex numbers. |
| [`CoordinateSystem`](manim.mobject.graphing.coordinate_systems.CoordinateSystem.md#manim.mobject.graphing.coordinate_systems.CoordinateSystem) | Abstract base class for Axes and NumberPlane.                                                                                                                               |
| [`NumberPlane`](manim.mobject.graphing.coordinate_systems.NumberPlane.md#manim.mobject.graphing.coordinate_systems.NumberPlane)                | Creates a cartesian plane with background lines.                                                                                                                            |
| [`PolarPlane`](manim.mobject.graphing.coordinate_systems.PolarPlane.md#manim.mobject.graphing.coordinate_systems.PolarPlane)                   | Creates a polar plane with background lines.                                                                                                                                |
| [`ThreeDAxes`](manim.mobject.graphing.coordinate_systems.ThreeDAxes.md#manim.mobject.graphing.coordinate_systems.ThreeDAxes)                   | A 3-dimensional set of axes.                                                                                                                                                |
