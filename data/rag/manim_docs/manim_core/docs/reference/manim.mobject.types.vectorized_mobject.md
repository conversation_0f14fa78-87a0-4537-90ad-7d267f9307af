# vectorized_mobject

Mobjects that use vector graphics.

### Classes

| [`CurvesAsSubmobjects`](manim.mobject.types.vectorized_mobject.CurvesAsSubmobjects.md#manim.mobject.types.vectorized_mobject.CurvesAsSubmobjects)   | Convert a curve's elements to submobjects.                                                                                                              |
|-----------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`DashedVMobject`](manim.mobject.types.vectorized_mobject.DashedVMobject.md#manim.mobject.types.vectorized_mobject.DashedVMobject)                  | A [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject) composed of dashes instead of lines. |
| [`VDict`](manim.mobject.types.vectorized_mobject.VDict.md#manim.mobject.types.vectorized_mobject.VDict)                                             | A VGroup-like class, also offering submobject access by key, like a python dict                                                                         |
| [`VGroup`](manim.mobject.types.vectorized_mobject.VGroup.md#manim.mobject.types.vectorized_mobject.VGroup)                                          | A group of vectorized mobjects.                                                                                                                         |
| [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject)                                    | A vectorized mobject.                                                                                                                                   |
| [`VectorizedPoint`](manim.mobject.types.vectorized_mobject.VectorizedPoint.md#manim.mobject.types.vectorized_mobject.VectorizedPoint)               |                                                                                                                                                         |
