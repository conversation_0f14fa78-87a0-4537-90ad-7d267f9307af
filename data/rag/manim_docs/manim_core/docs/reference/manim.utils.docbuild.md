# docbuild

Utilities for building the Manim documentation.

For more information about the Manim documentation building, see:

- [Manim Development Process](../contributing/development.md), specifically the `Documentation`
  bullet point under [Polishing Changes and Submitting a Pull Request](../contributing/development.md#polishing-changes-and-submitting-a-pull-request)
- [Adding Documentation](../contributing/docs.md)

| [`autoaliasattr_directive`](manim.utils.docbuild.autoaliasattr_directive.md#module-manim.utils.docbuild.autoaliasattr_directive)   | A directive for documenting type aliases and other module-level attributes.   |
|------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------|
| [`autocolor_directive`](manim.utils.docbuild.autocolor_directive.md#module-manim.utils.docbuild.autocolor_directive)               | A directive for documenting colors in Manim.                                  |
| [`manim_directive`](manim.utils.docbuild.manim_directive.md#module-manim.utils.docbuild.manim_directive)                           | A directive for including Manim videos in a Sphinx document                   |
| [`module_parsing`](manim.utils.docbuild.module_parsing.md#module-manim.utils.docbuild.module_parsing)                              | Read and parse all the Manim modules and extract documentation from them.     |
