# 优化模式配置文件
# 此配置文件启用了批量场景实现模式，先生成所有场景的视觉故事板，然后技术实现文档，最后一起生成代码

# 模型配置
model:
  type: "openai/gpt-4o-mini"
  api:
    openrouter_api_key: "${OPENROUTER_API_KEY}"
    openrouter_api_base_url: "https://openrouter.ai/api/v1"

# 文件配置
files:
  output_dir: "output"

# 工作流配置
workflow:
  # 工作流控制
  start_stage: null  # 从头开始
  enable_rag: true
  enable_context_learning: true
  enable_visual_fix: true
  
  # 【关键配置】启用优化的场景实现模式
  use_optimized_implementation: true
  
  # 步骤开关配置
  step_switches:
    enable_scene_plan: true
    enable_vision_storyboard: true
    enable_technical_implementation: true
    enable_animation_narration: true
    enable_code_generation: true
    enable_rendering: true
    enable_evaluation: true
    enable_video_combine: true
  
  # 文件路径配置
  scene_plan_file: "output/{topic}/scene_outline.txt"
  vision_storyboard_dir: "output/{topic}/vision_storyboard"
  technical_implementation_dir: "output/{topic}/technical_implementation"
  animation_narration_dir: "output/{topic}/animation_narration"
  code_dir: "output/{topic}/code"
  video_dir: "output/{topic}/videos"
  evaluation_file: "output/{topic}/evaluation.json"
  
  # RAG相关配置
  chroma_db_path: "data/rag/chroma_db"
  manim_docs_path: "data/rag/manim_docs"
  embedding_model: "text-embedding-3-large"

# 代理配置
agents:
  # 示例解释配置
  example_explain:
    topic: "BPE算法"
    purpose: "详细解释BPE（Byte Pair Encoding）算法的工作原理，包括算法步骤、数学原理和实际应用示例"

# Manim配置
manim:
  quality: "l"  # 低质量用于快速测试
  format: "mp4" 