# BraceText

Qualified name: `manim.mobject.svg.brace.BraceText`

### *class* BraceText(obj, text, label_constructor=<class 'manim.mobject.text.tex_mobject.Tex'>, \*\*kwargs)

Bases: [`<PERSON><PERSON><PERSON><PERSON><PERSON>`](manim.mobject.svg.brace.BraceLabel.md#manim.mobject.svg.brace.BraceLabel)

### Methods

### Attributes

| `animate`             | Used to animate the application of any method of `self`.               |
|-----------------------|------------------------------------------------------------------------|
| `animation_overrides` |                                                                        |
| `color`               |                                                                        |
| `depth`               | The depth of the mobject.                                              |
| `fill_color`          | If there are multiple colors (for gradient) this returns the first one |
| `height`              | The height of the mobject.                                             |
| `n_points_per_curve`  |                                                                        |
| `sheen_factor`        |                                                                        |
| `stroke_color`        |                                                                        |
| `width`               | The width of the mobject.                                              |

#### \_original_\_init_\_(obj, text, label_constructor=<class 'manim.mobject.text.tex_mobject.Tex'>, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.
