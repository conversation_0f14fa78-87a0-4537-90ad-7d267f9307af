# value_tracker

Simple mobjects that can be used for storing (and updating) a value.

### Classes

| [`ComplexValueTracker`](manim.mobject.value_tracker.ComplexValueTracker.md#manim.mobject.value_tracker.ComplexValueTracker)   | Tracks a complex-valued parameter.                                |
|-------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------|
| [`ValueTracker`](manim.mobject.value_tracker.ValueTracker.md#manim.mobject.value_tracker.ValueTracker)                        | A mobject that can be used for tracking (real-valued) parameters. |
