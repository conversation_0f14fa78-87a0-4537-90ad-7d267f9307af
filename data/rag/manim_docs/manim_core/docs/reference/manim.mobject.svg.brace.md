# brace

Mobject representing curly braces.

### Classes

| [`<PERSON>Brace`](manim.mobject.svg.brace.ArcBrace.md#manim.mobject.svg.brace.ArcBrace)                               | Creates a [`Brace`](manim.mobject.svg.brace.Brace.md#manim.mobject.svg.brace.Brace) that wraps around an [`Arc`](manim.mobject.geometry.arc.Arc.md#manim.mobject.geometry.arc.Arc).   |
|------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`Brace`](manim.mobject.svg.brace.Brace.md#manim.mobject.svg.brace.Brace)                                        | Takes a mobject and draws a brace adjacent to it.                                                                                                                                     |
| [`BraceBetweenPoints`](manim.mobject.svg.brace.BraceBetweenPoints.md#manim.mobject.svg.brace.BraceBetweenPoints) | Similar to Brace, but instead of taking a mobject it uses 2 points to place the brace.                                                                                                |
| [`BraceLabel`](manim.mobject.svg.brace.BraceLabel.md#manim.mobject.svg.brace.BraceLabel)                         | Create a brace with a label attached.                                                                                                                                                 |
| [`BraceText`](manim.mobject.svg.brace.BraceText.md#manim.mobject.svg.brace.BraceText)                            |                                                                                                                                                                                       |
