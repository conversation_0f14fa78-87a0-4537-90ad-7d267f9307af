# tex_file_writing

Interface for writing, compiling, and converting `.tex` files.

#### SEE ALSO
`mobject.svg.tex_mobject`

### Functions

### compile_tex(tex_file, tex_compiler, output_format)

Compiles a tex_file into a .dvi or a .xdv or a .pdf

* **Parameters:**
  * **tex_file** (*Path*) – File name of TeX file to be typeset.
  * **tex_compiler** (*str*) – String containing the compiler to be used, e.g. `pdflatex` or `lualatex`
  * **output_format** (*str*) – String containing the output format generated by the compiler, e.g. `.dvi` or `.pdf`
* **Returns:**
  Path to generated output file in desired format (DVI, XDV or PDF).
* **Return type:**
  `Path`

### convert_to_svg(dvi_file, extension, page=1)

Converts a .dvi, .xdv, or .pdf file into an svg using dvisvgm.

* **Parameters:**
  * **dvi_file** (*Path*) – File name of the input file to be converted.
  * **extension** (*str*) – String containing the file extension and thus indicating the file type, e.g. `.dvi` or `.pdf`
  * **page** (*int*) – Page to be converted if input file is multi-page.
* **Returns:**
  Path to generated SVG file.
* **Return type:**
  `Path`

### delete_nonsvg_files(additional_endings=())

Deletes every file that does not have a suffix in `(".svg", ".tex", *additional_endings)`

* **Parameters:**
  **additional_endings** (*Iterable* *[**str* *]*) – Additional endings to whitelist
* **Return type:**
  None

### generate_tex_file(expression, environment=None, tex_template=None)

Takes a tex expression (and an optional tex environment),
and returns a fully formed tex file ready for compilation.

* **Parameters:**
  * **expression** (*str*) – String containing the TeX expression to be rendered, e.g. `\\sqrt{2}` or `foo`
  * **environment** (*str* *|* *None*) – The string containing the environment in which the expression should be typeset, e.g. `align*`
  * **tex_template** ([*TexTemplate*](manim.utils.tex.TexTemplate.md#manim.utils.tex.TexTemplate) *|* *None*) – Template class used to typesetting. If not set, use default template set via config[“tex_template”]
* **Returns:**
  Path to generated TeX file
* **Return type:**
  `Path`

### insight_inputenc_error(matching)

* **Parameters:**
  **matching** (*Match* *[**str* *]*)
* **Return type:**
  *Generator*[str]

### insight_package_not_found_error(matching)

* **Parameters:**
  **matching** (*Match* *[**str* *]*)
* **Return type:**
  *Generator*[str]

### make_tex_compilation_command(tex_compiler, output_format, tex_file, tex_dir)

Prepares the TeX compilation command, i.e. the TeX compiler name
and all necessary CLI flags.

* **Parameters:**
  * **tex_compiler** (*str*) – String containing the compiler to be used, e.g. `pdflatex` or `lualatex`
  * **output_format** (*str*) – String containing the output format generated by the compiler, e.g. `.dvi` or `.pdf`
  * **tex_file** (*Path*) – File name of TeX file to be typeset.
  * **tex_dir** (*Path*) – Path to the directory where compiler output will be stored.
* **Returns:**
  Compilation command according to given parameters
* **Return type:**
  `list[str]`

### print_all_tex_errors(log_file, tex_compiler, tex_file)

* **Parameters:**
  * **log_file** (*Path*)
  * **tex_compiler** (*str*)
  * **tex_file** (*Path*)
* **Return type:**
  None

### print_tex_error(tex_compilation_log, error_start_index, tex_source)

* **Parameters:**
  * **tex_compilation_log** (*Sequence* *[**str* *]*)
  * **error_start_index** (*int*)
  * **tex_source** (*Sequence* *[**str* *]*)
* **Return type:**
  None

### tex_hash(expression)

* **Parameters:**
  **expression** (*Any*)
* **Return type:**
  str

### tex_to_svg_file(expression, environment=None, tex_template=None)

Takes a tex expression and returns the svg version of the compiled tex

* **Parameters:**
  * **expression** (*str*) – String containing the TeX expression to be rendered, e.g. `\\sqrt{2}` or `foo`
  * **environment** (*str* *|* *None*) – The string containing the environment in which the expression should be typeset, e.g. `align*`
  * **tex_template** ([*TexTemplate*](manim.utils.tex.TexTemplate.md#manim.utils.tex.TexTemplate) *|* *None*) – Template class used to typesetting. If not set, use default template set via config[“tex_template”]
* **Returns:**
  Path to generated SVG file.
* **Return type:**
  `Path`
