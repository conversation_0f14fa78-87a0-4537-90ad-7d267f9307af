# frames_comparison

### Functions

### frames_comparison(func=None, \*, last_frame=True, renderer_class=<class 'manim.renderer.cairo_renderer.CairoRenderer'>, base_scene=<class 'manim.scene.scene.Scene'>, \*\*custom_config)

Compares the frames generated by the test with control frames previously registered.

If there is no control frames for this test, the test will fail. To generate
control frames for a given test, pass `--set_test` flag to pytest
while running the test.

Note that this decorator can be use with or without parentheses.

* **Parameters:**
  * **last_frame** (*bool*) – whether the test should test the last frame, by default True.
  * **renderer_class** (*type* *[**CairoRenderer* *|* *OpenGLRenderer* *]*) – The base renderer to use (OpenGLRenderer/CairoRenderer), by default CairoRenderer
  * **base_scene** (*type* *[*[*Scene*](manim.scene.scene.Scene.md#manim.scene.scene.Scene) *]*) – The base class for the scene (ThreeDScene, etc.), by default Scene
  * **warning::** ( *..*) – By default, last_frame is True, which means that only the last frame is tested.
    If the scene has a moving animation, then the test must set last_frame to False.
  * **func** (*Callable* *|* *None*)
  * **custom_config** (*Any*)
* **Return type:**
  *Callable*
