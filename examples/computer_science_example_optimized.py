from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate


class ComputerScienceExampleOptimized(ProfessionalScienceTemplate):
    """优化的计算机科学算法讲解示例 - 简化版快速排序演示"""
    
    def construct(self):
        # 设置背景
        self.setup_background()
        
        # 演示快速排序算法
        self.demonstrate_quicksort_algorithm()
    
    def demonstrate_quicksort_algorithm(self):
        """演示快速排序算法的核心过程"""
        
        # 初始化数据
        self.numbers = [64, 34, 25, 12, 22, 11, 38]
        
        # 创建各区域内容
        title_group = self.create_title_region_content("快速排序")
        step_group = self.create_step_region_content("算法演示")
        
        # 创建主要的排序可视化
        main_content = self.create_sorting_visualization()
        main_group = self.create_main_region_content(main_content)
        
        # 创建辅助信息
        left_aux = self.create_left_auxiliary_content("特点:", [
            "• 分治算法",
            "• 平均O(nlogn)",
            "• 原地排序"
        ])
        
        result_group = self.create_result_region_content(
            "快速排序：选择基准，分区，递归排序"
        )
        
        # 初始展示
        self.play(Write(title_group))
        self.play(Write(step_group), FadeIn(left_aux))
        self.play(FadeIn(main_group))
        self.play(Write(result_group))
        self.wait(1)
        
        # 执行排序演示
        self.perform_sorting_demo(step_group, result_group)
    
    def create_sorting_visualization(self):
        """创建简化的排序可视化"""
        # 创建数字圆圈
        self.circles = VGroup()
        
        for i, num in enumerate(self.numbers):
            circle = Circle(
                radius=0.35,
                fill_color=self.colors['accent'],
                fill_opacity=0.8,
                stroke_color=self.colors['primary'],
                stroke_width=2
            )
            
            number = Text(
                str(num), 
                font_size=18, 
                color=WHITE, 
                weight=BOLD
            ).move_to(circle.get_center())
            
            # 水平排列
            x_offset = (i - len(self.numbers)/2 + 0.5) * 0.8
            circle.shift(RIGHT * x_offset)
            number.move_to(circle.get_center())
            
            circle_group = VGroup(circle, number)
            self.circles.add(circle_group)
        
        # 添加数组标签
        array_label = Text(
            "待排序数组", 
            font_size=16, 
            color=self.colors['text_primary']
        ).next_to(self.circles, UP, buff=0.4)
        
        return VGroup(self.circles, array_label)
    
    def perform_sorting_demo(self, step_group, result_group):
        """执行简化的排序演示"""
        
        # 第一步：选择基准
        step1 = self.create_step_region_content("选择基准")
        self.play(Transform(step_group, step1))
        
        # 高亮最后一个元素作为基准
        pivot_index = len(self.numbers) - 1
        self.highlight_element(pivot_index, self.colors['persistent'])
        
        result1 = self.create_result_region_content(f"选择 {self.numbers[pivot_index]} 作为基准元素")
        self.play(Transform(result_group, result1))
        self.wait(1)
        
        # 第二步：分区过程
        step2 = self.create_step_region_content("分区操作")
        self.play(Transform(step_group, step2))
        
        pivot_value = self.numbers[pivot_index]
        partition_index = self.simulate_partition(pivot_value)
        
        result2 = self.create_result_region_content(f"分区完成：小于 {pivot_value} 的在左侧")
        self.play(Transform(result_group, result2))
        self.wait(1)
        
        # 第三步：基准归位
        step3 = self.create_step_region_content("基准归位")
        self.play(Transform(step_group, step3))
        
        # 将基准移动到正确位置
        self.move_pivot_to_position(pivot_index, partition_index)
        self.numbers[partition_index], self.numbers[pivot_index] = self.numbers[pivot_index], self.numbers[partition_index]
        
        result3 = self.create_result_region_content(f"基准 {pivot_value} 已就位，左右子数组继续排序")
        self.play(Transform(result_group, result3))
        self.wait(1)
        
        # 第四步：展示最终结果
        step4 = self.create_step_region_content("排序完成")
        self.play(Transform(step_group, step4))
        
        # 展示排序后的数组（简化版本）
        sorted_numbers = sorted(self.numbers)
        self.show_final_result(sorted_numbers)
        
        final_result = self.create_result_region_content(f"排序完成：{sorted_numbers}")
        self.play(Transform(result_group, final_result))
        self.wait(2)
    
    def highlight_element(self, index, color):
        """简单高亮指定元素"""
        if index < len(self.circles):
            circle = self.circles[index][0]  # 获取圆圈对象
            self.play(
                circle.animate.set_stroke(color=color, width=4),
                run_time=0.5
            )
    
    def simulate_partition(self, pivot_value):
        """模拟分区过程（简化版本）"""
        # 找到应该的分区位置
        smaller_count = sum(1 for num in self.numbers[:-1] if num <= pivot_value)
        
        # 简单的颜色变化来表示分区
        for i, num in enumerate(self.numbers[:-1]):  # 除了基准元素
            if num <= pivot_value:
                self.highlight_element(i, self.colors['success'])
            else:
                self.highlight_element(i, self.colors['highlight'])
        
        self.wait(1)
        return smaller_count
    
    def move_pivot_to_position(self, from_index, to_index):
        """将基准元素移动到正确位置"""
        if from_index != to_index and to_index < len(self.circles):
            from_circle = self.circles[from_index]
            to_position = self.circles[to_index].get_center()
            
            # 简单的移动动画
            self.play(
                from_circle.animate.move_to(to_position),
                run_time=1.0
            )
            
            # 更新circles顺序
            self.circles.submobjects[from_index], self.circles.submobjects[to_index] = \
                self.circles.submobjects[to_index], self.circles.submobjects[from_index]
    
    def show_final_result(self, sorted_numbers):
        """展示最终排序结果"""
        # 将所有圆圈变为成功色
        for i, circle_group in enumerate(self.circles):
            circle = circle_group[0]
            number_text = circle_group[1]
            
            # 更新数字显示
            new_number = Text(
                str(sorted_numbers[i]), 
                font_size=18, 
                color=WHITE, 
                weight=BOLD
            ).move_to(circle.get_center())
            
            self.play(
                circle.animate.set_fill(color=self.colors['success'], opacity=0.8),
                Transform(number_text, new_number),
                run_time=0.3
            )
        
        self.wait(1)


# 使用示例
if __name__ == "__main__":
    # 运行优化版快速排序演示
    # manim computer_science_example_optimized.py ComputerScienceExampleOptimized -p
    pass 