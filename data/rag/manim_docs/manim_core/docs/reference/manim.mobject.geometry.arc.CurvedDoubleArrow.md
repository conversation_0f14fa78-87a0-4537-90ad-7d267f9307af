# CurvedDoubleArrow

Qualified name: `manim.mobject.geometry.arc.CurvedDoubleArrow`

### *class* CurvedDoubleArrow(start_point, end_point, \*\*kwargs)

Bases: [`CurvedArrow`](manim.mobject.geometry.arc.CurvedArrow.md#manim.mobject.geometry.arc.CurvedArrow)

### Methods

### Attributes

| `animate`             | Used to animate the application of any method of `self`.               |
|-----------------------|------------------------------------------------------------------------|
| `animation_overrides` |                                                                        |
| `color`               |                                                                        |
| `depth`               | The depth of the mobject.                                              |
| `fill_color`          | If there are multiple colors (for gradient) this returns the first one |
| `height`              | The height of the mobject.                                             |
| `n_points_per_curve`  |                                                                        |
| `sheen_factor`        |                                                                        |
| `stroke_color`        |                                                                        |
| `width`               | The width of the mobject.                                              |
* **Parameters:**
  * **start_point** ([*Point3DLike*](manim.typing.md#manim.typing.Point3DLike))
  * **end_point** ([*Point3DLike*](manim.typing.md#manim.typing.Point3DLike))
  * **kwargs** (*Any*)

#### \_original_\_init_\_(start_point, end_point, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.

* **Parameters:**
  * **start_point** ([*Point3DLike*](manim.typing.md#manim.typing.Point3DLike))
  * **end_point** ([*Point3DLike*](manim.typing.md#manim.typing.Point3DLike))
  * **kwargs** (*Any*)
* **Return type:**
  None
