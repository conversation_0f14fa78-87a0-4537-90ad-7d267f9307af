# AliasAttrDocumenter

Qualified name: `manim.utils.docbuild.autoaliasattr\_directive.AliasAttrDocumenter`

### *class* AliasAttrDocumenter(name, arguments, options, content, lineno, content_offset, block_text, state, state_machine)

Bases: `Directive`

Directive which replaces <PERSON><PERSON>nx’s Autosummary for module-level
attributes: instead, it manually crafts a new “Type Aliases”
section, where all the module-level attributes which are explicitly
annotated as `TypeAlias` are considered as such, for their
use all around the Manim docs.

These type aliases are separated from the “regular” module-level
attributes, which get their traditional “Module Attributes”
section autogenerated with Sphinx’s Autosummary under “Type
Aliases”.

See `docs/source/_templates/autosummary/module.rst` to watch
this directive in action.

See [`parse_module_attributes()`](manim.utils.docbuild.module_parsing.md#manim.utils.docbuild.module_parsing.parse_module_attributes) for more information on how
the modules are parsed to obtain the `TypeAlias` information
and separate it from the other attributes.

### Methods

| `run`   |    |
|---------|----|

### Attributes

| `final_argument_whitespace`                                                                                  | May the final argument contain whitespace?                 |
|--------------------------------------------------------------------------------------------------------------|------------------------------------------------------------|
| [`has_content`](#manim.utils.docbuild.autoaliasattr_directive.AliasAttrDocumenter.has_content)               | May the directive have content?                            |
| `objtype`                                                                                                    |                                                            |
| `option_spec`                                                                                                | Mapping of option names to validator functions.            |
| `optional_arguments`                                                                                         | Number of optional arguments after the required arguments. |
| [`required_arguments`](#manim.utils.docbuild.autoaliasattr_directive.AliasAttrDocumenter.required_arguments) | Number of required directive arguments.                    |

#### has_content *= True*

May the directive have content?

#### required_arguments *= 1*

Number of required directive arguments.
