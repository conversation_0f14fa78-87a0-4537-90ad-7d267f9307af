from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate


class QuickSortProfessionalScene(ProfessionalScienceTemplate):
    """快速排序专业教学场景"""
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 初始化数组数据
        self.initial_array = [6, 3, 7, 2, 8, 5, 1, 4]
        self.current_array = self.initial_array.copy()
        
        # 执行各个阶段
        self.stage1_introduction()
        self.stage2_first_partition_setup()
        self.stage3_first_partition_round1()
        self.stage4_first_partition_round2()
        self.stage5_first_partition_complete()
        self.stage6_left_subarray_partition()
        self.stage7_right_subarray_partition()
        self.stage8_final_result()
    
    def create_array_group(self, array_data, highlight_indices=None, pivot_index=None):
        """创建数组可视化Group"""
        squares = []
        numbers = []
        
        for i, num in enumerate(array_data):
            # 创建方块
            square = Square(side_length=0.8, color=WHITE, fill_opacity=0.1)
            # 创建数字
            number = Text(str(num), font_size=36, color=WHITE)
            number.move_to(square.get_center())
            
            # 高亮显示
            if highlight_indices and i in highlight_indices:
                square.set_fill(YELLOW, opacity=0.3)
            if pivot_index == i:
                square.set_fill(RED, opacity=0.5)
                
            squares.append(square)
            numbers.append(number)
        
        # 排列方块
        array_group = VGroup(*squares)
        array_group.arrange(RIGHT, buff=0.1)
        
        # 添加数字
        for i, number in enumerate(numbers):
            number.move_to(squares[i].get_center())
        
        final_group = VGroup(array_group, VGroup(*numbers))
        return final_group
    
    def create_pointer_group(self, array_group, i_index, j_index):
        """创建指针Group"""
        squares = array_group[0]  # 获取方块组
        
        # 左指针 i
        i_arrow = Arrow(
            start=squares[i_index].get_top() + UP * 0.5,
            end=squares[i_index].get_top() + UP * 0.1,
            color=BLUE, buff=0
        )
        i_label = Text("i", font_size=24, color=BLUE)
        i_label.next_to(i_arrow, UP, buff=0.1)
        
        # 右指针 j
        j_arrow = Arrow(
            start=squares[j_index].get_bottom() + DOWN * 0.5,
            end=squares[j_index].get_bottom() + DOWN * 0.1,
            color=GREEN, buff=0
        )
        j_label = Text("j", font_size=24, color=GREEN)
        j_label.next_to(j_arrow, DOWN, buff=0.1)
        
        return VGroup(i_arrow, i_label, j_arrow, j_label)
    
    def create_swap_animation_group(self, array_group, index1, index2):
        """创建交换动画指示Group"""
        squares = array_group[0]
        
        # 创建交换箭头
        arrow1 = CurvedArrow(
            start_point=squares[index1].get_center() + UP * 0.3,
            end_point=squares[index2].get_center() + UP * 0.3,
            color=ORANGE
        )
        arrow2 = CurvedArrow(
            start_point=squares[index2].get_center() + DOWN * 0.3,
            end_point=squares[index1].get_center() + DOWN * 0.3,
            color=ORANGE
        )
        
        return VGroup(arrow1, arrow2)
    
    def stage1_introduction(self):
        """阶段1：引入初始玩具陈列"""
        # 标题区域
        title_group = self.create_title_region_content("玩具整理")
        self.play(Write(title_group))
        self.region_elements['title'] = title_group
        
        # 步骤区域
        step_group = self.create_step_region_content("快速排序实操演练")
        self.play(Write(step_group))
        self.region_elements['step'] = step_group
        
        # 主内容区域 - 初始数组
        initial_array_group = self.create_array_group(self.current_array)
        main_content = VGroup(initial_array_group)
        main_region = self.create_main_region_content(main_content)
        self.play(Create(main_region))
        self.region_elements['main'] = main_region
        
        # 保存当前数组组件引用
        self.current_array_group = initial_array_group
        
        self.wait(2)
    
    def stage2_first_partition_setup(self):
        """阶段2：第一次分区 - 选择基准与初始指针"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤1：第一次分区")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 左辅助区域
        left_aux = self.create_left_auxiliary_content(
            "要点", 
            ["选择基准：第一个玩具", "约定：选择子区间第一个玩具"]
        )
        self.play(FadeIn(left_aux))
        self.region_elements['left_aux'] = left_aux
        
        # 创建带基准高亮的数组
        highlighted_array = self.create_array_group(self.current_array, pivot_index=0)
        
        # 创建指针
        pointer_group = self.create_pointer_group(highlighted_array, 0, 7)
        
        # 更新主内容区域
        new_main_content = VGroup(highlighted_array, pointer_group)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新引用
        self.current_array_group = highlighted_array
        self.current_pointer_group = pointer_group
        
        self.wait(2)
    
    def stage3_first_partition_round1(self):
        """阶段3：第一次分区 - 查找并交换(第一轮)"""
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content(
            "规则",
            ["两边找玩具：右找小，左找大"]
        )
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 创建交换指示
        swap_animation = self.create_swap_animation_group(self.current_array_group, 2, 7)
        
        # 执行交换动画
        self.play(Create(swap_animation))
        self.wait(1)
        
        # 更新数组数据
        self.current_array[2], self.current_array[7] = self.current_array[7], self.current_array[2]
        
        # 创建新的数组和指针
        new_array = self.create_array_group(self.current_array, pivot_index=0)
        new_pointers = self.create_pointer_group(new_array, 2, 7)
        
        # 更新主内容
        new_main_content = VGroup(new_array, new_pointers)
        new_main_region = self.create_main_region_content(new_main_content)
        
        self.play(
            FadeOut(swap_animation),
            ReplacementTransform(self.region_elements['main'], new_main_region)
        )
        self.region_elements['main'] = new_main_region
        
        # 更新引用
        self.current_array_group = new_array
        self.current_pointer_group = new_pointers
        
        self.wait(2)
    
    def stage4_first_partition_round2(self):
        """阶段4：第一次分区 - 查找并交换(第二轮)"""
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content(
            "操作",
            ["继续查找并交换"]
        )
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 创建交换指示 (8和1)
        swap_animation = self.create_swap_animation_group(self.current_array_group, 4, 6)
        
        # 执行交换动画
        self.play(Create(swap_animation))
        self.wait(1)
        
        # 更新数组数据
        self.current_array[4], self.current_array[6] = self.current_array[6], self.current_array[4]
        
        # 创建新的数组和指针
        new_array = self.create_array_group(self.current_array, pivot_index=0)
        new_pointers = self.create_pointer_group(new_array, 4, 6)
        
        # 更新主内容
        new_main_content = VGroup(new_array, new_pointers)
        new_main_region = self.create_main_region_content(new_main_content)
        
        self.play(
            FadeOut(swap_animation),
            ReplacementTransform(self.region_elements['main'], new_main_region)
        )
        self.region_elements['main'] = new_main_region
        
        # 更新引用
        self.current_array_group = new_array
        self.current_pointer_group = new_pointers
        
        self.wait(2)
    
    def stage5_first_partition_complete(self):
        """阶段5：第一次分区 - 指针相遇与基准归位"""
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content(
            "状态",
            ["继续移动，直至相遇"]
        )
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 基准与j指针位置交换 (6和5)
        swap_animation = self.create_swap_animation_group(self.current_array_group, 0, 5)
        
        # 执行交换动画
        self.play(Create(swap_animation))
        self.wait(1)
        
        # 更新数组数据
        self.current_array[0], self.current_array[5] = self.current_array[5], self.current_array[0]
        
        # 创建带分区线的数组
        partitioned_array = self.create_array_group(self.current_array, pivot_index=5)
        
        # 创建分区线
        squares = partitioned_array[0]
        partition_line = Line(
            start=squares[5].get_left() + LEFT * 0.1 + UP * 0.8,
            end=squares[5].get_left() + LEFT * 0.1 + DOWN * 0.8,
            color=RED, stroke_width=3
        )
        partition_line2 = Line(
            start=squares[5].get_right() + RIGHT * 0.1 + UP * 0.8,
            end=squares[5].get_right() + RIGHT * 0.1 + DOWN * 0.8,
            color=RED, stroke_width=3
        )
        
        # 更新主内容
        new_main_content = VGroup(partitioned_array, partition_line, partition_line2)
        new_main_region = self.create_main_region_content(new_main_content)
        
        self.play(
            FadeOut(swap_animation),
            ReplacementTransform(self.region_elements['main'], new_main_region)
        )
        self.region_elements['main'] = new_main_region
        
        # 右辅助区域
        right_aux = self.create_right_auxiliary_content(
            "分区",
            ["分区结果：小于6，大于6"]
        )
        self.play(FadeIn(right_aux))
        self.region_elements['right_aux'] = right_aux
        
        # 结果区域
        result_group = self.create_result_region_content("第一次分区完成，6已归位")
        self.play(Write(result_group))
        self.region_elements['result'] = result_group
        
        self.wait(3)
    
    def stage6_left_subarray_partition(self):
        """阶段6：对左边子数组进行分区"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤2：左边子数组分区")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content(
            "操作",
            ["选择基准：5"]
        )
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 创建左子数组
        left_subarray = [5, 3, 4, 2, 1]
        sub_array_group = self.create_array_group(left_subarray, pivot_index=0)
        
        # 模拟分区过程 (简化显示)
        # 显示5归位后的结果 [1, 3, 4, 2, 5]
        left_partitioned = [1, 3, 4, 2, 5]
        partitioned_sub_array = self.create_array_group(left_partitioned, pivot_index=4)
        
        # 更新主内容
        new_main_content = VGroup(partitioned_sub_array)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "结果",
            ["分区结果：小于5，大于5（空）"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果区域
        new_result = self.create_result_region_content("左边子数组分区完成，5已归位")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(3)
    
    def stage7_right_subarray_partition(self):
        """阶段7：对右边子数组进行分区"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤3：右边子数组分区")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 更新左辅助区域
        new_left_aux = self.create_left_auxiliary_content(
            "操作",
            ["选择基准：8"]
        )
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 创建右子数组
        right_subarray = [8, 7]
        sub_array_group = self.create_array_group(right_subarray, pivot_index=0)
        
        # 显示8归位后的结果 [7, 8]
        right_partitioned = [7, 8]
        partitioned_sub_array = self.create_array_group(right_partitioned, pivot_index=1)
        
        # 更新主内容
        new_main_content = VGroup(partitioned_sub_array)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 更新右辅助区域
        new_right_aux = self.create_right_auxiliary_content(
            "结果",
            ["分区结果：小于8，大于8（空）"]
        )
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 更新结果区域
        new_result = self.create_result_region_content("右边子数组分区完成，8已归位")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result
        
        self.wait(3)
    
    def stage8_final_result(self):
        """阶段8：递归过程与最终结果"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤N：重复分区至完全有序")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 清除辅助区域
        self.play(
            FadeOut(self.region_elements['left_aux']),
            FadeOut(self.region_elements['right_aux'])
        )
        
        # 显示最终排序结果
        final_array = [1, 2, 3, 4, 5, 6, 7, 8]
        final_array_group = self.create_array_group(final_array)
        
        # 放大显示最终结果
        final_array_group.scale(1.2)
        
        # 更新主内容
        new_main_content = VGroup(final_array_group)
        new_main_region = self.create_main_region_content(new_main_content)
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region))
        self.region_elements['main'] = new_main_region
        
        # 最终结果描述
        final_result = self.create_result_region_content("所有玩具按大小排好：[1, 2, 3, 4, 5, 6, 7, 8]")
        self.play(ReplacementTransform(self.region_elements['result'], final_result))
        self.region_elements['result'] = final_result
        
        self.wait(4)

# 运行场景的方法
if __name__ == "__main__":
    scene = QuickSortProfessionalScene()
    scene.render() 