# boolean_ops

Boolean operations for two-dimensional mobjects.

### Classes

| [`Difference`](manim.mobject.geometry.boolean_ops.Difference.md#manim.mobject.geometry.boolean_ops.Difference)       | Subtracts one [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject) from another one.   |
|----------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------|
| [`Exclusion`](manim.mobject.geometry.boolean_ops.Exclusion.md#manim.mobject.geometry.boolean_ops.Exclusion)          | Find the XOR between two [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject).         |
| [`Intersection`](manim.mobject.geometry.boolean_ops.Intersection.md#manim.mobject.geometry.boolean_ops.Intersection) | Find the intersection of two [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject) s.   |
| [`Union`](manim.mobject.geometry.boolean_ops.Union.md#manim.mobject.geometry.boolean_ops.Union)                      | Union of two or more [`VMobject`](manim.mobject.types.vectorized_mobject.VMobject.md#manim.mobject.types.vectorized_mobject.VMobject) s.           |
