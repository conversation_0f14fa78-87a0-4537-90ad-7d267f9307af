# DVIPSNAMES

dvips Colors

This module contains the colors defined in the dvips driver, which are commonly accessed
as named colors in LaTeX via the `\usepackage[dvipsnames]{xcolor}` package.

To use the colors from this list, access them directly from the module (which
is exposed to <PERSON><PERSON>’s global name space):

```pycon
>>> from manim import DVIPSNAMES
>>> DVIPSNAMES.DARKORCHID
ManimColor('#A4538A')
```

## List of Color Constants

These hex values are derived from those specified in the `xcolor` package
documentation (see [https://ctan.org/pkg/xcolor](https://ctan.org/pkg/xcolor)):

| Color Name       | RGB Hex Code                                                                                                                                     | Color Name       | RGB Hex Code                                                                                                                                     |
|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|
| `APRICOT`        | <div style="background-color:#FBB982;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FBB982</code></div> | `AQUAMARINE`     | <div style="background-color:#00B5BE;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00B5BE</code></div> |
| `BITTERSWEET`    | <div style="background-color:#C04F17;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#C04F17</code></div> | `BLACK`          | <div style="background-color:#221E1F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#221E1F</code></div> |
| `BLUE`           | <div style="background-color:#2D2F92;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#2D2F92</code></div> | `BLUEGREEN`      | <div style="background-color:#00B3B8;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00B3B8</code></div> |
| `BLUEVIOLET`     | <div style="background-color:#473992;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#473992</code></div> | `BRICKRED`       | <div style="background-color:#B6321C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#B6321C</code></div> |
| `BROWN`          | <div style="background-color:#792500;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#792500</code></div> | `BURNTORANGE`    | <div style="background-color:#F7921D;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F7921D</code></div> |
| `CADETBLUE`      | <div style="background-color:#74729A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#74729A</code></div> | `CARNATIONPINK`  | <div style="background-color:#F282B4;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F282B4</code></div> |
| `CERULEAN`       | <div style="background-color:#00A2E3;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00A2E3</code></div> | `CORNFLOWERBLUE` | <div style="background-color:#41B0E4;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#41B0E4</code></div> |
| `CYAN`           | <div style="background-color:#00AEEF;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00AEEF</code></div> | `DANDELION`      | <div style="background-color:#FDBC42;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FDBC42</code></div> |
| `DARKORCHID`     | <div style="background-color:#A4538A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#A4538A</code></div> | `EMERALD`        | <div style="background-color:#00A99D;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00A99D</code></div> |
| `FORESTGREEN`    | <div style="background-color:#009B55;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#009B55</code></div> | `FUCHSIA`        | <div style="background-color:#8C368C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#8C368C</code></div> |
| `GOLDENROD`      | <div style="background-color:#FFDF42;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFDF42</code></div> | `GRAY`           | <div style="background-color:#949698;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#949698</code></div> |
| `GREEN`          | <div style="background-color:#00A64F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#00A64F</code></div> | `GREENYELLOW`    | <div style="background-color:#DFE674;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DFE674</code></div> |
| `JUNGLEGREEN`    | <div style="background-color:#00A99A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00A99A</code></div> | `LAVENDER`       | <div style="background-color:#F49EC4;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F49EC4</code></div> |
| `LIMEGREEN`      | <div style="background-color:#8DC73E;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#8DC73E</code></div> | `MAGENTA`        | <div style="background-color:#EC008C;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#EC008C</code></div> |
| `MAHOGANY`       | <div style="background-color:#A9341F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#A9341F</code></div> | `MAROON`         | <div style="background-color:#AF3235;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#AF3235</code></div> |
| `MELON`          | <div style="background-color:#F89E7B;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F89E7B</code></div> | `MIDNIGHTBLUE`   | <div style="background-color:#006795;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#006795</code></div> |
| `MULBERRY`       | <div style="background-color:#A93C93;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#A93C93</code></div> | `NAVYBLUE`       | <div style="background-color:#006EB8;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#006EB8</code></div> |
| `OLIVEGREEN`     | <div style="background-color:#3C8031;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#3C8031</code></div> | `ORANGE`         | <div style="background-color:#F58137;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F58137</code></div> |
| `ORANGERED`      | <div style="background-color:#ED135A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#ED135A</code></div> | `ORCHID`         | <div style="background-color:#AF72B0;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#AF72B0</code></div> |
| `PEACH`          | <div style="background-color:#F7965A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F7965A</code></div> | `PERIWINKLE`     | <div style="background-color:#7977B8;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#7977B8</code></div> |
| `PINEGREEN`      | <div style="background-color:#008B72;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#008B72</code></div> | `PLUM`           | <div style="background-color:#92268F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#92268F</code></div> |
| `PROCESSBLUE`    | <div style="background-color:#00B0F0;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00B0F0</code></div> | `PURPLE`         | <div style="background-color:#99479B;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#99479B</code></div> |
| `RAWSIENNA`      | <div style="background-color:#974006;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#974006</code></div> | `RED`            | <div style="background-color:#ED1B23;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#ED1B23</code></div> |
| `REDORANGE`      | <div style="background-color:#F26035;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#F26035</code></div> | `REDVIOLET`      | <div style="background-color:#A1246B;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#A1246B</code></div> |
| `RHODAMINE`      | <div style="background-color:#EF559F;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#EF559F</code></div> | `ROYALBLUE`      | <div style="background-color:#0071BC;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#0071BC</code></div> |
| `ROYALPURPLE`    | <div style="background-color:#613F99;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#613F99</code></div> | `RUBINERED`      | <div style="background-color:#ED017D;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#ED017D</code></div> |
| `SALMON`         | <div style="background-color:#F69289;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#F69289</code></div> | `SEAGREEN`       | <div style="background-color:#3FBC9D;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#3FBC9D</code></div> |
| `SEPIA`          | <div style="background-color:#671800;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#671800</code></div> | `SKYBLUE`        | <div style="background-color:#46C5DD;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#46C5DD</code></div> |
| `SPRINGGREEN`    | <div style="background-color:#C6DC67;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#C6DC67</code></div> | `TAN`            | <div style="background-color:#DA9D76;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#DA9D76</code></div> |
| `TEALBLUE`       | <div style="background-color:#00AEB3;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00AEB3</code></div> | `THISTLE`        | <div style="background-color:#D883B7;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#D883B7</code></div> |
| `TURQUOISE`      | <div style="background-color:#00B4CE;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#00B4CE</code></div> | `VIOLET`         | <div style="background-color:#58429B;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#58429B</code></div> |
| `VIOLETRED`      | <div style="background-color:#EF58A0;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#EF58A0</code></div> | `WHITE`          | <div style="background-color:#FFFFFF;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFFFFF</code></div> |
| `WILDSTRAWBERRY` | <div style="background-color:#EE2967;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:white;">#EE2967</code></div> | `YELLOW`         | <div style="background-color:#FFF200;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FFF200</code></div> |
| `YELLOWGREEN`    | <div style="background-color:#98CC70;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#98CC70</code></div> | `YELLOWORANGE`   | <div style="background-color:#FAA21A;padding: 0.25rem 0;border-radius:8px;margin: 0.5rem 0.2rem"><code style="color:black;">#FAA21A</code></div> |
