# ArrowCircleTip

Qualified name: `manim.mobject.geometry.tips.ArrowCircleTip`

### *class* ArrowCircleTip(fill_opacity=0, stroke_width=3, length=0.35, start_angle=3.141592653589793, \*\*kwargs)

Bases: [`ArrowTip`](manim.mobject.geometry.tips.ArrowTip.md#manim.mobject.geometry.tips.ArrowTip), [`Circle`](manim.mobject.geometry.arc.Circle.md#manim.mobject.geometry.arc.Circle)

Circular arrow tip.

### Methods

### Attributes

| `animate`             | Used to animate the application of any method of `self`.               |
|-----------------------|------------------------------------------------------------------------|
| `animation_overrides` |                                                                        |
| `base`                | The base point of the arrow tip.                                       |
| `color`               |                                                                        |
| `depth`               | The depth of the mobject.                                              |
| `fill_color`          | If there are multiple colors (for gradient) this returns the first one |
| `height`              | The height of the mobject.                                             |
| `length`              | The length of the arrow tip.                                           |
| `n_points_per_curve`  |                                                                        |
| `sheen_factor`        |                                                                        |
| `stroke_color`        |                                                                        |
| `tip_angle`           | The angle of the arrow tip.                                            |
| `tip_point`           | The tip point of the arrow tip.                                        |
| `vector`              | The vector pointing from the base point to the tip point.              |
| `width`               | The width of the mobject.                                              |
* **Parameters:**
  * **fill_opacity** (*float*)
  * **stroke_width** (*float*)
  * **length** (*float*)
  * **start_angle** (*float*)
  * **kwargs** (*Any*)

#### \_original_\_init_\_(fill_opacity=0, stroke_width=3, length=0.35, start_angle=3.141592653589793, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.

* **Parameters:**
  * **fill_opacity** (*float*)
  * **stroke_width** (*float*)
  * **length** (*float*)
  * **start_angle** (*float*)
  * **kwargs** (*Any*)
* **Return type:**
  None
