from manim import *
import numpy as np

class Word2VecFruitStoreScene(MovingCameraScene):
    def construct(self):
        """
        Manim scene based on the provided vision storyboard for Word2Vec.
        """
        # --- General Setup ---
        title = Text("水果店里的“语义魔法”", font="SimHei").to_edge(UP)
        self.play(Write(title))
        self.wait(1)

        # --- Phase 1: Build Vocabulary & Initialize Vectors ---
        explanation1 = Text("阶段1：构建词典并初始化词向量", font="SimHei", font_size=28).to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        corpus_text = VGroup(
            Text("我 买 了 香蕉", font="SimHei"),
            Text("我 爱 吃 橘子", font="SimHei"),
            Text("苹果 也 很 好吃", font="SimHei")
        ).arrange(DOWN, buff=0.5).move_to(LEFT * 3)

        self.play(Write(explanation1))
        self.play(Write(corpus_text))
        self.wait(2)

        vocab = ["我", "买", "了", "香蕉", "爱", "吃", "橘子", "苹果", "也", "很", "好吃"]
        initial_vectors = {word: np.round(np.random.rand(3), 2) for word in vocab}

        table_data = [["词语", "初始词向量 (3D)"]]
        table_data.extend([[word, str(initial_vectors[word])] for word in vocab])
        
        vocab_table = Table(
            table_data,
            include_outer_lines=True,
            line_config={"stroke_width": 1, "color": WHITE}
        ).scale(0.4).to_edge(RIGHT, buff=1)

        self.play(
            corpus_text.animate.scale(0.7).to_edge(LEFT),
            Create(vocab_table)
        )
        self.wait(3)
        self.play(FadeOut(explanation1), FadeOut(corpus_text))

        # --- Phase 2: Generate (Center, Context) Pairs ---
        explanation2 = Text("阶段2：生成“中心词-上下文词”对", font="SimHei", font_size=28).to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        # Simplified for animation
        corpus_highlight = VGroup(
            Text("我 买 了 香蕉", font="SimHei"),
        ).move_to(ORIGIN).shift(UP*2)
        
        center_word = corpus_highlight[0][2:3] # "了"
        context_words = VGroup(corpus_highlight[0][0:2], corpus_highlight[0][3:5])

        self.play(FadeOut(vocab_table), Write(explanation2))
        self.play(Write(corpus_highlight))
        self.play(Indicate(center_word, color=BLUE))
        self.play(Indicate(context_words, color=YELLOW))

        pairs = VGroup(
            Text("(了, 我)"), Text("(了, 买)"), Text("(了, 香蕉)")
        ).arrange(DOWN, buff=0.3).next_to(corpus_highlight, DOWN, buff=1)

        self.play(TransformFromCopy(VGroup(center_word, context_words), pairs))
        self.wait(2)
        self.play(FadeOut(explanation2), FadeOut(corpus_highlight), FadeOut(pairs))

        # --- Phase 3: Model Training (Simplified) ---
        explanation3 = Text("阶段3：模型训练与词向量更新", font="SimHei", font_size=28).to_edge(RIGHT, buff=0.5).shift(UP*2)
        
        # Update vectors to be more meaningful for the final step
        initial_vectors["橘子"] = np.round(initial_vectors["香蕉"] + np.random.uniform(-0.2, 0.2, 3), 2)
        initial_vectors["苹果"] = np.round(initial_vectors["香蕉"] + np.random.uniform(-0.8, 0.8, 3), 2)
        
        updated_table_data = [["词语", "词向量"]]
        updated_table_data.extend([[word, str(initial_vectors[word])] for word in vocab])
        
        training_table = Table(updated_table_data, include_outer_lines=True).scale(0.4).move_to(LEFT*3)
        
        self.play(Write(explanation3), FadeIn(training_table))
        
        banana_row = training_table.get_rows()[4]
        orange_row = training_table.get_rows()[7]
        self.play(Indicate(banana_row, color=GREEN), Indicate(orange_row, color=GREEN))

        formula = MathTex(r"\text{sim}(A, B) = \frac{A \cdot B}{\|A\| \|B\|}").next_to(training_table, RIGHT, buff=1)
        self.play(Write(formula))

        # Simulate vector update
        new_banana_vec = np.round(initial_vectors["香蕉"] * 0.8 + initial_vectors["橘子"] * 0.2, 2)
        new_orange_vec = np.round(initial_vectors["橘子"] * 0.8 + initial_vectors["香蕉"] * 0.2, 2)

        updated_banana_text = Text(str(new_banana_vec), font_size=16)
        updated_orange_text = Text(str(new_orange_vec), font_size=16)
        
        updated_banana_text.move_to(training_table.get_cell((5, 2)))
        updated_orange_text.move_to(training_table.get_cell((8, 2)))

        self.play(
            Transform(training_table.get_entries((5, 2)), updated_banana_text),
            Transform(training_table.get_entries((8, 2)), updated_orange_text)
        )
        self.wait(2)
        
        initial_vectors["香蕉"] = new_banana_vec
        initial_vectors["橘子"] = new_orange_vec
        
        self.play(FadeOut(explanation3), FadeOut(formula))

        # --- Phase 4: Final Vectors & Application ---
        explanation4 = Text("阶段4：应用最终向量进行推荐", font="SimHei", font_size=28).to_edge(RIGHT, buff=0.5).shift(UP*2)
        final_table_title = Text("最终词向量", font="SimHei", font_size=24).next_to(training_table, UP, buff=0.2)
        
        self.play(Write(explanation4), Write(final_table_title))
        
        # Calculate similarities
        sim_banana_orange = np.dot(initial_vectors["香蕉"], initial_vectors["橘子"]) / (np.linalg.norm(initial_vectors["香蕉"]) * np.linalg.norm(initial_vectors["橘子"]))
        sim_banana_apple = np.dot(initial_vectors["香蕉"], initial_vectors["苹果"]) / (np.linalg.norm(initial_vectors["香蕉"]) * np.linalg.norm(initial_vectors["苹果"]))

        sim_results = VGroup(
            MathTex(r"\text{sim(banana, orange)} = ", f"{sim_banana_orange:.2f}").set_color(GREEN),
            MathTex(r"\text{sim(banana, apple)} = ", f"{sim_banana_apple:.2f}").set_color(RED)
        ).arrange(DOWN, buff=0.5).next_to(training_table, RIGHT, buff=1)

        self.play(Write(sim_results))
        self.play(Indicate(sim_results[0], scale_factor=1.2, color=GREEN))
        self.wait(1)

        recommendation_flow = VGroup(
            Text("输入: 香蕉", font="SimHei"),
            Arrow(LEFT, RIGHT),
            Text("计算相似度", font="SimHei"),
            Arrow(LEFT, RIGHT),
            Text("推荐: ?", font="SimHei")
        ).arrange(RIGHT).next_to(training_table, DOWN, buff=1).shift(RIGHT*3)
        
        self.play(Write(recommendation_flow))
        self.wait(1)
        
        final_recommendation = Text("推荐: 橘子", font="SimHei").move_to(recommendation_flow[-1]).set_color(GREEN)
        self.play(Transform(recommendation_flow[-1], final_recommendation))
        
        self.wait(3)
        
        # Final fade out
        self.play(
            FadeOut(title),
            FadeOut(explanation4),
            FadeOut(training_table),
            FadeOut(final_table_title),
            FadeOut(sim_results),
            FadeOut(recommendation_flow)
        )
        self.wait(1)
