# Mobject1D

Qualified name: `manim.mobject.types.point\_cloud\_mobject.Mobject1D`

### *class* Mobject1D(density=10, \*\*kwargs)

Bases: [`PMobject`](manim.mobject.types.point_cloud_mobject.PMobject.md#manim.mobject.types.point_cloud_mobject.PMobject)

### Methods

| `add_line`   |    |
|--------------|----|

### Attributes

| `animate`             | Used to animate the application of any method of `self`.   |
|-----------------------|------------------------------------------------------------|
| `animation_overrides` |                                                            |
| `depth`               | The depth of the mobject.                                  |
| `height`              | The height of the mobject.                                 |
| `width`               | The width of the mobject.                                  |
* **Parameters:**
  * **density** (*int*)
  * **kwargs** (*Any*)

#### \_original_\_init_\_(density=10, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.

* **Parameters:**
  * **density** (*int*)
  * **kwargs** (*Any*)
* **Return type:**
  None
