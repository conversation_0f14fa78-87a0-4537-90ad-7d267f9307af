# Mobject2D

Qualified name: `manim.mobject.types.point\_cloud\_mobject.Mobject2D`

### *class* Mobject2D(density=25, \*\*kwargs)

Bases: [`PMobject`](manim.mobject.types.point_cloud_mobject.PMobject.md#manim.mobject.types.point_cloud_mobject.PMobject)

### Methods

### Attributes

| `animate`             | Used to animate the application of any method of `self`.   |
|-----------------------|------------------------------------------------------------|
| `animation_overrides` |                                                            |
| `depth`               | The depth of the mobject.                                  |
| `height`              | The height of the mobject.                                 |
| `width`               | The width of the mobject.                                  |
* **Parameters:**
  * **density** (*int*)
  * **kwargs** (*Any*)

#### \_original_\_init_\_(density=25, \*\*kwargs)

Initialize self.  See help(type(self)) for accurate signature.

* **Parameters:**
  * **density** (*int*)
  * **kwargs** (*Any*)
* **Return type:**
  None
