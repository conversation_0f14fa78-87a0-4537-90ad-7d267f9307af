# geometry

Various geometric Mobjects.

## Modules

| [`arc`](manim.mobject.geometry.arc.md#module-manim.mobject.geometry.arc)                                  | Mobjects that are curved.                                                                                                                                |
|-----------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
| [`boolean_ops`](manim.mobject.geometry.boolean_ops.md#module-manim.mobject.geometry.boolean_ops)          | Boolean operations for two-dimensional mobjects.                                                                                                         |
| [`labeled`](manim.mobject.geometry.labeled.md#module-manim.mobject.geometry.labeled)                      | Mobjects that inherit from lines and contain a label along the length.                                                                                   |
| [`line`](manim.mobject.geometry.line.md#module-manim.mobject.geometry.line)                               | Mobjects that are lines or variations of them.                                                                                                           |
| [`polygram`](manim.mobject.geometry.polygram.md#module-manim.mobject.geometry.polygram)                   | Mobjects that are simple geometric shapes.                                                                                                               |
| [`shape_matchers`](manim.mobject.geometry.shape_matchers.md#module-manim.mobject.geometry.shape_matchers) | Mobjects used to mark and annotate other mobjects.                                                                                                       |
| [`tips`](manim.mobject.geometry.tips.md#module-manim.mobject.geometry.tips)                               | A collection of tip mobjects for use with [`TipableVMobject`](manim.mobject.geometry.arc.TipableVMobject.md#manim.mobject.geometry.arc.TipableVMobject). |
