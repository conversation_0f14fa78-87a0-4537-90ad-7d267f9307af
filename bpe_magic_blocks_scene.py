from manim import *
import sys
import os

# 添加模板路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'prompts'))
from professional_science_template import ProfessionalScienceTemplate

class BPEMagicBlocksScene(ProfessionalScienceTemplate):
    """BPE算法魔法积木造词记专业教学场景"""
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 初始化数据
        self.init_data()
        
        # 执行各个阶段
        self.stage1_initialization()
        self.stage2_first_merge()
        self.stage3_second_merge()
        self.stage4_preset_completion()
        self.stage5_practical_application()
        self.stage6_scene_transition()
    
    def init_data(self):
        """初始化BPE算法数据"""
        # 原始语料
        self.corpus = "你好棒你真棒、你好酷你好棒"
        
        # 初始词表计数
        self.initial_vocab = {
            '你': 4, '好': 4, '棒': 3, '真': 1, '酷': 1
        }
        
        # 初始文本序列
        self.initial_sequences = [
            "你 好 棒 你 真 棒",
            "你 好 酷 你 好 棒"
        ]
        
        # 第一次合并后的数据
        self.step2_vocab = {
            '你': 0, '好': 0, '棒': 3, '你好': 4
        }
        self.step2_sequences = [
            "你好 棒 你 真 棒",
            "你好 酷 你 好 棒"
        ]
        self.step2_pairs = {
            ('你好', '棒'): 3, ('棒', '你'): 1, ('你', '好'): 1
        }
        
        # 第二次合并后的数据
        self.step3_vocab = {
            '真': 1, '酷': 1, '你好': 4, '你好棒': 3
        }
        self.step3_sequences = [
            "你好棒 你 真 棒",
            "你好 酷 你 好 棒"
        ]
        self.step3_pairs = {
            ('你好棒', '你'): 1, ('真', '棒'): 1, ('你好', '酷'): 1
        }
        
        # 最终词表
        self.final_vocab = ['你', '好', '棒', '真', '酷', '你好', '你好棒']
        
        # 编码示例
        self.encode_sentence = "你好棒真棒"
        self.encode_steps = [
            "1. 查找最长子词：你好棒",
            "2. 剩余部分：真棒",
            "3. 查找最长子词：真",
            "4. 剩余部分：棒"
        ]
        self.encode_result = "['你好棒', '真', '棒']"
    
    def create_corpus_display(self):
        """创建语料展示"""
        corpus_title = Text("原始语料：", font_size=20, color=WHITE)
        corpus_content = Text(self.corpus, font_size=18, color=YELLOW)
        
        corpus_group = VGroup(corpus_title, corpus_content)
        corpus_group.arrange(RIGHT, buff=0.2)
        
        return corpus_group
    
    def create_vocab_table(self, vocab_dict, title="词表"):
        """创建词表表格"""
        # 创建表格数据 - 只显示前4项
        table_data = [
            [Text("字符", font_size=16, weight=BOLD), Text("计数", font_size=16, weight=BOLD)]
        ]
        
        items = list(vocab_dict.items())[:4]  # 只取前4项
        for char, count in items:
            table_data.append([
                Text(char, font_size=18),
                Text(str(count), font_size=18)
            ])
        
        table = MobjectTable(
            table_data,
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        table_title = Text(title, font_size=16, color=WHITE)
        
        return VGroup(table_title, table).arrange(DOWN, buff=0.2)
    
    def create_sequence_display(self, sequences, title="当前文本序列："):
        """创建文本序列展示"""
        title_text = Text(title, font_size=16, color=WHITE)
        
        sequence_lines = VGroup()
        for seq in sequences:
            line = Text(seq, font_size=16, color=BLUE)
            sequence_lines.add(line)
        
        sequence_lines.arrange(DOWN, buff=0.2, aligned_edge=LEFT)
        
        return VGroup(title_text, sequence_lines).arrange(DOWN, buff=0.3)
    
    def create_pair_count_table(self, pairs_dict, title="相邻子词对计数："):
        """创建字符对计数表格"""
        title_text = Text(title, font_size=16, color=WHITE)
        
        # 创建表格数据 - 只显示前3项
        table_data = [
            [Text("字符对", font_size=16, weight=BOLD), Text("计数", font_size=16, weight=BOLD)]
        ]
        
        items = list(pairs_dict.items())[:3]  # 只取前3项
        for (char1, char2), count in items:
            pair_str = f"({char1}, {char2})"
            table_data.append([
                Text(pair_str, font_size=14),
                Text(str(count), font_size=14)
            ])
        
        table = MobjectTable(
            table_data,
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": WHITE}
        )
        
        return VGroup(title_text, table).arrange(DOWN, buff=0.2)
    
    def create_merge_info(self, pair, freq, new_token):
        """创建合并信息显示"""
        freq_text = Text(f"最高频对为 '{pair}'，出现 {freq}次", font_size=18, color=RED)
        new_token_text = Text(f"新的'积木'：'{new_token}'", font_size=18, color=GREEN)
        
        return VGroup(freq_text, new_token_text).arrange(DOWN, buff=0.3)
    
    def create_final_vocab_display(self):
        """创建最终词表展示"""
        preset_text = Text("预设合并次数：2次", font_size=18, color=WHITE)
        
        # 创建最终词表 - 只显示前4项
        vocab_data = [
            [Text("最终子词词表", font_size=16, weight=BOLD, color=GREEN)]
        ]
        
        for i, token in enumerate(self.final_vocab[:4]):
            vocab_data.append([Text(token, font_size=16)])
        
        vocab_table = MobjectTable(
            vocab_data,
            include_outer_lines=True,
            h_buff=0.3,
            v_buff=0.2,
            line_config={"stroke_width": 1, "color": GREEN}
        )
        
        return VGroup(preset_text, vocab_table).arrange(DOWN, buff=0.4)
    
    def create_encoding_demo(self):
        """创建编码演示"""
        # 待编码句子
        sentence_text = Text(f"待编码句子：'{self.encode_sentence}'", font_size=18, color=WHITE)
        
        # 匹配过程
        process_title = Text("匹配过程：", font_size=16, color=WHITE)
        process_steps = VGroup()
        for step in self.encode_steps:
            step_text = Text(step, font_size=14, color=BLUE)
            process_steps.add(step_text)
        
        process_steps.arrange(DOWN, buff=0.2, aligned_edge=LEFT)
        
        # 编码结果
        result_text = Text(f"编码结果：{self.encode_result}", font_size=16, color=GREEN, weight=BOLD)
        
        encoding_group = VGroup(
            sentence_text,
            VGroup(process_title, process_steps).arrange(DOWN, buff=0.3),
            result_text
        ).arrange(DOWN, buff=0.4)
        
        return encoding_group
    
    def stage1_initialization(self):
        """阶段1：背景设定与步骤1引入"""
        # 标题区域
        title_group = self.create_title_region_content("魔法积木造词记")
        self.play(title_group.animate.shift(DOWN * 0.5))  # 从上方滑入效果
        self.region_elements['title'] = title_group
        
        # 步骤区域
        step_group = self.create_step_region_content("步骤1：初始化词表")
        self.play(step_group.animate.shift(RIGHT * 0.5))  # 从左侧滑入效果
        self.region_elements['step'] = step_group
        
        # 主内容区域 - 语料、初始词表、文本序列、字符对计数
        corpus_display = self.create_corpus_display()
        vocab_table = self.create_vocab_table(self.initial_vocab, "初始词表")
        sequence_display = self.create_sequence_display(self.initial_sequences)
        
        # 初始字符对计数
        initial_pairs = {('你', '好'): 4, ('好', '棒'): 3, ('棒', '你'): 2, ('你', '真'): 1}
        pair_count_table = self.create_pair_count_table(initial_pairs, "相邻汉字对计数：")
        
        # 组织主内容
        main_content_left = VGroup(corpus_display, vocab_table).arrange(DOWN, buff=0.5)
        main_content_right = VGroup(sequence_display, pair_count_table).arrange(DOWN, buff=0.5)
        main_content = VGroup(main_content_left, main_content_right).arrange(RIGHT, buff=0.8)
        
        main_region = self.create_main_region_content(main_content)
        
        # 动画序列
        self.play(FadeIn(corpus_display))  # 语料逐渐显现
        self.wait(1)
        self.play(Create(vocab_table), Write(sequence_display))  # 词表和序列同步显示
        self.wait(1)
        self.play(Write(pair_count_table))  # 字符对计数显示
        
        self.region_elements['main'] = main_region
        self.wait(2)
    
    def stage2_first_merge(self):
        """阶段2：步骤2：第一次合并"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤2：第一次合并")
        self.play(Transform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 淡出前一阶段内容
        self.play(FadeOut(self.region_elements['main']))
        
        # 新的主内容
        merge_info = self.create_merge_info("你 好", 4, "你好")
        updated_vocab = self.create_vocab_table(self.step2_vocab, "更新词表")
        updated_sequences = self.create_sequence_display(self.step2_sequences, "文本序列更新：")
        updated_pairs = self.create_pair_count_table(self.step2_pairs, "相邻子词对计数：")
        
        # 组织新内容
        main_content_left = VGroup(merge_info, updated_vocab).arrange(DOWN, buff=0.5)
        main_content_right = VGroup(updated_sequences, updated_pairs).arrange(DOWN, buff=0.5)
        new_main_content = VGroup(main_content_left, main_content_right).arrange(RIGHT, buff=0.8)
        
        new_main_region = self.create_main_region_content(new_main_content)
        
        # 动画序列
        self.play(Write(merge_info))  # 显示最高频对
        self.wait(1)
        self.play(Create(updated_vocab))  # 显示新的积木
        self.wait(1)
        self.play(Write(updated_sequences), Create(updated_pairs))  # 更新序列和计数
        
        self.region_elements['main'] = new_main_region
        self.wait(2)
    
    def stage3_second_merge(self):
        """阶段3：步骤3：第二次合并"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤3：第二次合并")
        self.play(Transform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 新的主内容
        merge_info_s3 = self.create_merge_info("你好 棒", 3, "你好棒")
        updated_vocab_s3 = self.create_vocab_table(self.step3_vocab, "更新词表")
        updated_sequences_s3 = self.create_sequence_display(self.step3_sequences, "文本序列更新：")
        updated_pairs_s3 = self.create_pair_count_table(self.step3_pairs, "相邻子词对计数：")
        
        # 组织新内容
        main_content_left_s3 = VGroup(merge_info_s3, updated_vocab_s3).arrange(DOWN, buff=0.5)
        main_content_right_s3 = VGroup(updated_sequences_s3, updated_pairs_s3).arrange(DOWN, buff=0.5)
        new_main_content_s3 = VGroup(main_content_left_s3, main_content_right_s3).arrange(RIGHT, buff=0.8)
        
        new_main_region_s3 = self.create_main_region_content(new_main_content_s3)
        
        # 使用ReplacementTransform进行平滑过渡
        self.play(ReplacementTransform(self.region_elements['main'], new_main_region_s3))
        self.region_elements['main'] = new_main_region_s3
        self.wait(3)
    
    def stage4_preset_completion(self):
        """阶段4：步骤4：达到预设合并次数"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤4：达到预设合并次数")
        self.play(Transform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 淡出主内容
        self.play(FadeOut(self.region_elements['main']))
        
        # 结果区域 - 最终词表
        final_vocab_display = self.create_final_vocab_display()
        result_group = self.create_result_region_content("算法停止条件达成")
        
        # 动画序列
        self.play(Write(final_vocab_display))  # 显示预设条件
        self.wait(1)
        self.play(Write(result_group))  # 显示最终词表
        
        self.region_elements['result'] = result_group
        self.final_vocab_display = final_vocab_display
        self.wait(3)
    
    def stage5_practical_application(self):
        """阶段5：步骤5：实际应用（文本编码）"""
        # 更新步骤
        new_step = self.create_step_region_content("步骤5：实际应用")
        self.play(Transform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 淡出结果区域
        self.play(FadeOut(self.region_elements['result']), FadeOut(self.final_vocab_display))
        
        # 主内容区域 - 编码演示
        encoding_demo = self.create_encoding_demo()
        main_region = self.create_main_region_content(encoding_demo)
        
        # 动画序列
        self.play(Write(encoding_demo[0]))  # 待编码句子
        self.wait(1)
        
        # 逐步显示匹配过程
        process_group = encoding_demo[1]
        self.play(Write(process_group[0]))  # 匹配过程标题
        for step in process_group[1]:
            self.play(Write(step))
            self.wait(0.5)
        
        self.wait(1)
        self.play(Write(encoding_demo[2]))  # 编码结果
        
        self.region_elements['main'] = main_region
        self.wait(3)
    
    def stage6_scene_transition(self):
        """阶段6：场景衔接"""
        # 淡出所有内容
        all_elements = [
            self.region_elements.get('title'),
            self.region_elements.get('step'), 
            self.region_elements.get('main')
        ]
        
        # 过滤None值
        valid_elements = [elem for elem in all_elements if elem is not None]
        
        if valid_elements:
            self.play(*[FadeOut(elem) for elem in valid_elements])
        
        self.wait(2)

# 运行场景的方法
if __name__ == "__main__":
    scene = BPEMagicBlocksScene()
    scene.render() 