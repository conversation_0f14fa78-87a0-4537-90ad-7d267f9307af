# tips

A collection of tip mobjects for use with [`TipableVMobject`](manim.mobject.geometry.arc.TipableVMobject.md#manim.mobject.geometry.arc.TipableVMobject).

### Classes

| [`ArrowCircleFilledTip`](manim.mobject.geometry.tips.ArrowCircleFilledTip.md#manim.mobject.geometry.tips.ArrowCircleFilledTip)       | Circular arrow tip with filled tip.   |
|--------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------|
| [`ArrowCircleTip`](manim.mobject.geometry.tips.ArrowCircleTip.md#manim.mobject.geometry.tips.ArrowCircleTip)                         | Circular arrow tip.                   |
| [`ArrowSquareFilledTip`](manim.mobject.geometry.tips.ArrowSquareFilledTip.md#manim.mobject.geometry.tips.ArrowSquareFilledTip)       | Square arrow tip with filled tip.     |
| [`ArrowSquareTip`](manim.mobject.geometry.tips.ArrowSquareTip.md#manim.mobject.geometry.tips.ArrowSquareTip)                         | Square arrow tip.                     |
| [`ArrowTip`](manim.mobject.geometry.tips.ArrowTip.md#manim.mobject.geometry.tips.ArrowTip)                                           | Base class for arrow tips.            |
| [`ArrowTriangleFilledTip`](manim.mobject.geometry.tips.ArrowTriangleFilledTip.md#manim.mobject.geometry.tips.ArrowTriangleFilledTip) | Triangular arrow tip with filled tip. |
| [`ArrowTriangleTip`](manim.mobject.geometry.tips.ArrowTriangleTip.md#manim.mobject.geometry.tips.ArrowTriangleTip)                   | Triangular arrow tip.                 |
| [`StealthTip`](manim.mobject.geometry.tips.StealthTip.md#manim.mobject.geometry.tips.StealthTip)                                     | 'Stealth' fighter / kite arrow shape. |
