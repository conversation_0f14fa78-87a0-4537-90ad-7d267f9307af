# testing

Utilities for Manim tests using [pytest](https://pytest.org).

For more information about Manim testing, see:

- [Manim Development Process](../contributing/development.md), specifically the `Tests` bullet
  point under [Polishing Changes and Submitting a Pull Request](../contributing/development.md#polishing-changes-and-submitting-a-pull-request)
- [Adding Tests](../contributing/testing.md)

| [`frames_comparison`](manim.utils.testing.frames_comparison.md#module-manim.utils.testing.frames_comparison)    |    |
|-----------------------------------------------------------------------------------------------------------------|----|
| [`_frames_testers`](manim.utils.testing._frames_testers.md#module-manim.utils.testing._frames_testers)          |    |
| [`_show_diff`](manim.utils.testing._show_diff.md#module-manim.utils.testing._show_diff)                         |    |
| [`_test_class_makers`](manim.utils.testing._test_class_makers.md#module-manim.utils.testing._test_class_makers) |    |
