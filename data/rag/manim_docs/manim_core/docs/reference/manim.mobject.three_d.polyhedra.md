# polyhedra

General polyhedral class and platonic solids.

### Classes

| [`ConvexHull3D`](manim.mobject.three_d.polyhedra.ConvexHull3D.md#manim.mobject.three_d.polyhedra.ConvexHull3D)   | A convex hull for a set of points                |
|------------------------------------------------------------------------------------------------------------------|--------------------------------------------------|
| [`Dodecahedron`](manim.mobject.three_d.polyhedra.Dodecahedron.md#manim.mobject.three_d.polyhedra.Dodecahedron)   | A dodecahedron, one of the five platonic solids. |
| [`Icosahedron`](manim.mobject.three_d.polyhedra.Icosahedron.md#manim.mobject.three_d.polyhedra.Icosahedron)      | An icosahedron, one of the five platonic solids. |
| [`Octahedron`](manim.mobject.three_d.polyhedra.Octahedron.md#manim.mobject.three_d.polyhedra.Octahedron)         | An octahedron, one of the five platonic solids.  |
| [`Polyhedron`](manim.mobject.three_d.polyhedra.Polyhedron.md#manim.mobject.three_d.polyhedra.Polyhedron)         | An abstract polyhedra class.                     |
| [`Tetrahedron`](manim.mobject.three_d.polyhedra.Tetrahedron.md#manim.mobject.three_d.polyhedra.Tetrahedron)      | A tetrahedron, one of the five platonic solids.  |
